export interface User {
  id: string;
  email: string;
  name: string;
}

export interface SearchCriteria {
  b2b: boolean;
  english: boolean;
  global: boolean;
  noExperience: boolean;
  commissionRate: number;
}

export interface Company {
  id: string;
  name: string;
  description: string;
  commissionRate: number;
  type: 'B2B' | 'B2C';
  language: string;
  location: string;
  experienceRequired: boolean;
}

export interface SearchResults {
  companies: Company[];
  totalCount: number;
  status: 'idle' | 'searching' | 'extracting' | 'completed' | 'error';
  progress: number;
  error?: string;
}

export type Screen = 'login' | 'search' | 'results' | 'error' | 'settings';