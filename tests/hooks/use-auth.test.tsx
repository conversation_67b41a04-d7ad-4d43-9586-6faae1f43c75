import React from 'react';
import { renderHook, act } from '@testing-library/react';
import { useAuth } from '../../src/hooks/use-auth';
import * as authServiceModule from '../../src/services/auth-service';

// Mock the auth service
jest.mock('../../src/services/auth-service');

const mockAuthService = {
  login: jest.fn(),
  logout: jest.fn(),
  getCurrentUser: jest.fn(),
  isAuthenticated: jest.fn(),
};

// Replace the authService export with our mock
(authServiceModule as any).authService = mockAuthService;

describe('useAuth', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockAuthService.getCurrentUser.mockReturnValue(null);
    mockAuthService.isAuthenticated.mockReturnValue(false);
  });

  it('should initialize with no user when not authenticated', () => {
    const { result } = renderHook(() => useAuth());

    expect(result.current.user).toBeNull();
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBeNull();
    expect(result.current.isAuthenticated).toBe(false);
  });

  it('should initialize with user when already authenticated', () => {
    const mockUser = {
      id: '1',
      email: '<EMAIL>',
      name: 'Test User',
    };

    mockAuthService.getCurrentUser.mockReturnValue(mockUser);
    mockAuthService.isAuthenticated.mockReturnValue(true);

    const { result } = renderHook(() => useAuth());

    expect(result.current.user).toEqual(mockUser);
    expect(result.current.isAuthenticated).toBe(true);
  });

  describe('login', () => {
    it('should login successfully', async () => {
      const mockUser = {
        id: '1',
        email: '<EMAIL>',
        name: 'Test User',
      };

      const mockAuthResponse = {
        user: mockUser,
        token: 'mock-token',
        refreshToken: 'mock-refresh-token',
      };

      mockAuthService.login.mockResolvedValue(mockAuthResponse);
      mockAuthService.isAuthenticated.mockReturnValue(true);

      const { result } = renderHook(() => useAuth());

      await act(async () => {
        await result.current.login('<EMAIL>', 'password123');
      });

      expect(mockAuthService.login).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
      });
      expect(result.current.user).toEqual(mockUser);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBeNull();
    });

    it('should handle login failure', async () => {
      const errorMessage = 'Invalid credentials';
      mockAuthService.login.mockRejectedValue(new Error(errorMessage));

      const { result } = renderHook(() => useAuth());

      await act(async () => {
        try {
          await result.current.login('<EMAIL>', 'wrongpassword');
        } catch (error) {
          // Expected to throw
        }
      });

      expect(result.current.user).toBeNull();
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(errorMessage);
    });

    it('should set loading state during login', async () => {
      let resolveLogin: (value: any) => void;
      const loginPromise = new Promise((resolve) => {
        resolveLogin = resolve;
      });

      mockAuthService.login.mockReturnValue(loginPromise);

      const { result } = renderHook(() => useAuth());

      act(() => {
        result.current.login('<EMAIL>', 'password123');
      });

      expect(result.current.isLoading).toBe(true);

      await act(async () => {
        resolveLogin!({
          user: { id: '1', email: '<EMAIL>', name: 'Test User' },
          token: 'mock-token',
          refreshToken: 'mock-refresh-token',
        });
        await loginPromise;
      });

      expect(result.current.isLoading).toBe(false);
    });
  });

  describe('logout', () => {
    it('should logout successfully', async () => {
      // Start with a logged-in user
      const mockUser = {
        id: '1',
        email: '<EMAIL>',
        name: 'Test User',
      };

      mockAuthService.getCurrentUser.mockReturnValue(mockUser);
      mockAuthService.isAuthenticated.mockReturnValue(true);
      mockAuthService.logout.mockResolvedValue(undefined);

      const { result } = renderHook(() => useAuth());

      // Initially authenticated
      expect(result.current.user).toEqual(mockUser);

      await act(async () => {
        await result.current.logout();
      });

      expect(mockAuthService.logout).toHaveBeenCalled();
      expect(result.current.user).toBeNull();
      expect(result.current.error).toBeNull();
      expect(result.current.isLoading).toBe(false);
    });

    it('should handle logout failure gracefully', async () => {
      mockAuthService.logout.mockRejectedValue(new Error('Logout failed'));

      const { result } = renderHook(() => useAuth());

      await act(async () => {
        await result.current.logout();
      });

      // Even if logout fails, local state should be cleared
      expect(result.current.user).toBeNull();
      expect(result.current.error).toBeNull();
    });

    it('should set loading state during logout', async () => {
      let resolveLogout: () => void;
      const logoutPromise = new Promise<void>((resolve) => {
        resolveLogout = resolve;
      });

      mockAuthService.logout.mockReturnValue(logoutPromise);

      const { result } = renderHook(() => useAuth());

      act(() => {
        result.current.logout();
      });

      expect(result.current.isLoading).toBe(true);

      await act(async () => {
        resolveLogout!();
        await logoutPromise;
      });

      expect(result.current.isLoading).toBe(false);
    });
  });

  describe('isAuthenticated', () => {
    it('should return true when user exists and service confirms authentication', () => {
      const mockUser = {
        id: '1',
        email: '<EMAIL>',
        name: 'Test User',
      };

      mockAuthService.getCurrentUser.mockReturnValue(mockUser);
      mockAuthService.isAuthenticated.mockReturnValue(true);

      const { result } = renderHook(() => useAuth());

      expect(result.current.isAuthenticated).toBe(true);
    });

    it('should return false when user exists but service denies authentication', () => {
      const mockUser = {
        id: '1',
        email: '<EMAIL>',
        name: 'Test User',
      };

      mockAuthService.getCurrentUser.mockReturnValue(mockUser);
      mockAuthService.isAuthenticated.mockReturnValue(false);

      const { result } = renderHook(() => useAuth());

      expect(result.current.isAuthenticated).toBe(false);
    });
  });
});
