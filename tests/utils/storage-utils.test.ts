import { StorageUtils, SessionStorageUtils } from '../../src/utils/storage-utils';

describe('storage-utils', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  describe('StorageUtils', () => {
    describe('setItem', () => {
      it('should store item successfully', () => {
        const mockSetItem = jest.spyOn(Storage.prototype, 'setItem');
        const testData = { name: 'John', age: 30 };

        const result = StorageUtils.setItem('testKey', testData);

        expect(result).toBe(true);
        expect(mockSetItem).toHaveBeenCalledWith('testKey', JSON.stringify(testData));
      });

      it('should handle storage errors gracefully', () => {
        const mockSetItem = jest.spyOn(Storage.prototype, 'setItem')
          .mockImplementation(() => {
            throw new Error('Storage quota exceeded');
          });
        const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

        const result = StorageUtils.setItem('testKey', 'testValue');

        expect(result).toBe(false);
        expect(consoleSpy).toHaveBeenCalledWith('Error saving to localStorage:', expect.any(Error));

        consoleSpy.mockRestore();
      });
    });

    describe('getItem', () => {
      it('should retrieve stored item successfully', () => {
        const testData = { name: 'John', age: 30 };
        const mockGetItem = jest.spyOn(Storage.prototype, 'getItem')
          .mockReturnValue(JSON.stringify(testData));

        const result = StorageUtils.getItem('testKey');

        expect(result).toEqual(testData);
        expect(mockGetItem).toHaveBeenCalledWith('testKey');
      });

      it('should return default value when item not found', () => {
        const mockGetItem = jest.spyOn(Storage.prototype, 'getItem')
          .mockReturnValue(null);
        const defaultValue = { default: true };

        const result = StorageUtils.getItem('nonExistentKey', defaultValue);

        expect(result).toEqual(defaultValue);
      });

      it('should return null when item not found and no default', () => {
        const mockGetItem = jest.spyOn(Storage.prototype, 'getItem')
          .mockReturnValue(null);

        const result = StorageUtils.getItem('nonExistentKey');

        expect(result).toBeNull();
      });

      it('should handle JSON parsing errors', () => {
        const mockGetItem = jest.spyOn(Storage.prototype, 'getItem')
          .mockReturnValue('invalid json');
        const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
        const defaultValue = { default: true };

        const result = StorageUtils.getItem('testKey', defaultValue);

        expect(result).toEqual(defaultValue);
        expect(consoleSpy).toHaveBeenCalledWith('Error reading from localStorage:', expect.any(Error));

        consoleSpy.mockRestore();
      });
    });

    describe('removeItem', () => {
      it('should remove item successfully', () => {
        const mockRemoveItem = jest.spyOn(Storage.prototype, 'removeItem');

        const result = StorageUtils.removeItem('testKey');

        expect(result).toBe(true);
        expect(mockRemoveItem).toHaveBeenCalledWith('testKey');
      });

      it('should handle removal errors gracefully', () => {
        const mockRemoveItem = jest.spyOn(Storage.prototype, 'removeItem')
          .mockImplementation(() => {
            throw new Error('Storage error');
          });
        const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

        const result = StorageUtils.removeItem('testKey');

        expect(result).toBe(false);
        expect(consoleSpy).toHaveBeenCalledWith('Error removing from localStorage:', expect.any(Error));

        consoleSpy.mockRestore();
      });
    });

    describe('clear', () => {
      it('should clear storage successfully', () => {
        const mockClear = jest.spyOn(Storage.prototype, 'clear');

        const result = StorageUtils.clear();

        expect(result).toBe(true);
        expect(mockClear).toHaveBeenCalled();
      });

      it('should handle clear errors gracefully', () => {
        const mockClear = jest.spyOn(Storage.prototype, 'clear')
          .mockImplementation(() => {
            throw new Error('Storage error');
          });
        const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

        const result = StorageUtils.clear();

        expect(result).toBe(false);
        expect(consoleSpy).toHaveBeenCalledWith('Error clearing localStorage:', expect.any(Error));

        consoleSpy.mockRestore();
      });
    });

    describe('isAvailable', () => {
      it('should return true when localStorage is available', () => {
        const mockSetItem = jest.spyOn(Storage.prototype, 'setItem');
        const mockRemoveItem = jest.spyOn(Storage.prototype, 'removeItem');

        const result = StorageUtils.isAvailable();

        expect(result).toBe(true);
        expect(mockSetItem).toHaveBeenCalledWith('__localStorage_test__', 'test');
        expect(mockRemoveItem).toHaveBeenCalledWith('__localStorage_test__');
      });

      it('should return false when localStorage is not available', () => {
        const mockSetItem = jest.spyOn(Storage.prototype, 'setItem')
          .mockImplementation(() => {
            throw new Error('Storage not available');
          });

        const result = StorageUtils.isAvailable();

        expect(result).toBe(false);
      });
    });
  });

  describe('SessionStorageUtils', () => {
    describe('setItem', () => {
      it('should store item in session storage successfully', () => {
        const testData = { session: 'data' };

        const result = SessionStorageUtils.setItem('sessionKey', testData);

        expect(result).toBe(true);
        expect(sessionStorage.setItem).toHaveBeenCalledWith('sessionKey', JSON.stringify(testData));
      });
    });

    describe('getItem', () => {
      it('should retrieve item from session storage successfully', () => {
        const testData = { session: 'data' };
        (sessionStorage.getItem as jest.Mock).mockReturnValue(JSON.stringify(testData));

        const result = SessionStorageUtils.getItem('sessionKey');

        expect(result).toEqual(testData);
      });
    });
  });
});
