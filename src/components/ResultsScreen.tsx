import React from 'react';
import { Download, X, CheckCircle, AlertCircle } from 'lucide-react';
import { SearchResults } from '../types';

interface ResultsScreenProps {
  results: SearchResults;
  onCancel: () => void;
  onDownload: () => void;
  onBackToSearch: () => void;
}

const ResultsScreen: React.FC<ResultsScreenProps> = ({
  results,
  onCancel,
  onDownload,
  onBackToSearch
}) => {
  const { status, progress, companies, totalCount, error } = results;

  const getStatusMessage = () => {
    switch (status) {
      case 'searching':
        return 'Searching for opportunities...';
      case 'extracting':
        return 'Extracting company data...';
      case 'completed':
        return `Search completed! Found ${totalCount} companies.`;
      case 'error':
        return error || 'An error occurred during the search.';
      default:
        return 'Preparing search...';
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-8 w-8 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-8 w-8 text-red-500" />;
      default:
        return (
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        );
    }
  };

  const isSearchInProgress = status === 'searching' || status === 'extracting';
  const isCompleted = status === 'completed';
  const hasError = status === 'error';

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Search Results
          </h1>
          <p className="text-lg text-gray-600">
            {isSearchInProgress ? 'Processing your search...' : 'Your search results are ready'}
          </p>
        </div>

        <div className="bg-white rounded-lg shadow-md p-8">
          {/* Status Section */}
          <div className="text-center mb-8">
            <div className="flex justify-center mb-4">
              {getStatusIcon()}
            </div>
            
            <h2 
              className={`text-xl font-semibold mb-2 ${
                hasError ? 'text-red-600' : 
                isCompleted ? 'text-green-600' : 'text-gray-900'
              }`}
              aria-live="polite"
            >
              {getStatusMessage()}
            </h2>

            {/* Progress Bar */}
            {isSearchInProgress && (
              <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
                <div 
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out"
                  style={{ width: `${progress}%` }}
                  role="progressbar"
                  aria-valuenow={progress}
                  aria-valuemin={0}
                  aria-valuemax={100}
                  aria-label={`Search progress: ${progress}%`}
                ></div>
              </div>
            )}

            {/* Progress Percentage */}
            {isSearchInProgress && (
              <p className="text-sm text-gray-500 mb-4">
                {progress}% complete
              </p>
            )}
          </div>

          {/* Results Summary */}
          {isCompleted && totalCount > 0 && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-green-800 mb-2">
                    Search Successful!
                  </h3>
                  <p className="text-green-700">
                    Found {totalCount} companies matching your criteria. 
                    {companies.length > 0 && ` Showing ${companies.length} results.`}
                  </p>
                </div>
                <CheckCircle className="h-12 w-12 text-green-500" />
              </div>
            </div>
          )}

          {/* No Results */}
          {isCompleted && totalCount === 0 && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-8">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-yellow-800 mb-2">
                    No Results Found
                  </h3>
                  <p className="text-yellow-700">
                    No companies match your search criteria. Try adjusting your filters and search again.
                  </p>
                </div>
                <AlertCircle className="h-12 w-12 text-yellow-500" />
              </div>
            </div>
          )}

          {/* Error State */}
          {hasError && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-6 mb-8">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-red-800 mb-2">
                    Search Error
                  </h3>
                  <p className="text-red-700">
                    {error || 'An unexpected error occurred. Please try again.'}
                  </p>
                </div>
                <AlertCircle className="h-12 w-12 text-red-500" />
              </div>
            </div>
          )}

          {/* Sample Results Preview */}
          {isCompleted && companies.length > 0 && (
            <div className="mb-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Sample Results Preview
              </h3>
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {companies.slice(0, 6).map((company, index) => (
                    <div key={index} className="bg-white p-4 rounded border">
                      <h4 className="font-medium text-gray-900 mb-2">
                        {company.name}
                      </h4>
                      <p className="text-sm text-gray-600 mb-2">
                        {company.description}
                      </p>
                      <div className="flex justify-between items-center text-sm">
                        <span className="text-green-600 font-medium">
                          {company.commissionRate}% commission
                        </span>
                        <span className="text-gray-500">
                          {company.type}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
                {companies.length > 6 && (
                  <p className="text-center text-gray-500 mt-4">
                    +{companies.length - 6} more results available in CSV download
                  </p>
                )}
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            {isSearchInProgress && (
              <button
                onClick={onCancel}
                className="flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
              >
                <X className="h-5 w-5 mr-2" />
                Cancel Search
              </button>
            )}

            {isCompleted && totalCount > 0 && (
              <button
                onClick={onDownload}
                className="flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors"
              >
                <Download className="h-5 w-5 mr-2" />
                Download CSV ({totalCount} companies)
              </button>
            )}

            {(isCompleted || hasError) && (
              <button
                onClick={onBackToSearch}
                className="flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
              >
                Back to Search
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResultsScreen;