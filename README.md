# COMCROW CSA - Professional Sales Opportunity Search Application

A modern React TypeScript application for finding and managing commission-based sales opportunities. COMCROW CSA helps sales professionals discover B2B and B2C companies offering attractive commission structures.

## 🚀 Features

- **Advanced Search**: Filter companies by commission rate, business type (B2B/B2C), language, location, and experience requirements
- **Real-time Results**: Live search with progress tracking and cancellation support
- **Data Export**: Export search results to CSV format for further analysis
- **Responsive Design**: Mobile-first design that works on all devices
- **Secure Authentication**: JWT-based authentication with session management
- **Modern UI**: Clean, professional interface built with Tailwind CSS

## 🛠️ Technology Stack

- **Frontend**: React 18 + TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS
- **Icons**: Lucide React
- **Code Quality**: ESLint + Prettier
- **Testing**: Jest + React Testing Library (planned)

## 📋 Prerequisites

- Node.js 18.0.0 or higher
- npm 8.0.0 or higher

## 🔧 Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/GEMDevEng/COMCROW_CSA.git
   cd COMCROW_CSA
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Start the development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   Navigate to `http://localhost:3000`

## 🎯 Usage

### Demo Credentials
For testing purposes, use these credentials:
- **Email**: <EMAIL>
- **Password**: password123

### Search Process
1. **Login** with your credentials
2. **Set Search Criteria** using the filters:
   - Business Type (B2B/B2C)
   - Language preference
   - Global availability
   - Experience requirements
   - Minimum commission rate
3. **Execute Search** and monitor progress
4. **Review Results** in the results screen
5. **Export Data** to CSV if needed

## 📁 Project Structure

```
COMCROW_CSA/
├── src/
│   ├── components/          # React components
│   │   ├── forms/          # Form components
│   │   ├── layout/         # Layout components
│   │   └── ui/             # UI components
│   ├── hooks/              # Custom React hooks
│   ├── services/           # API and business logic
│   ├── utils/              # Utility functions
│   ├── types/              # TypeScript definitions
│   ├── constants/          # Application constants
│   └── styles/             # CSS and styling
├── config/                 # Configuration files
├── docs/                   # Documentation
├── public/                 # Static assets
└── tests/                  # Test files (planned)
```

For detailed information, see [DIRECTORY_STRUCTURE.md](docs/DIRECTORY_STRUCTURE.md).

## 🔨 Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

### Development Guidelines

Please read our [PROJECT_RULES.md](docs/PROJECT_RULES.md) for:
- Coding standards
- File naming conventions
- Development workflow
- Code review process

## 🏗️ Architecture

The application follows a microservices-oriented architecture with clear separation of concerns:

- **Components**: Organized by function (forms, layout, UI)
- **Services**: Handle business logic and API interactions
- **Hooks**: Manage state and side effects
- **Utils**: Pure functions for common operations
- **Types**: Centralized TypeScript definitions

## 🔐 Environment Configuration

The application uses environment variables for configuration. See `.env.example` for all available options:

### Required Variables
- `VITE_API_BASE_URL` - Backend API URL
- `VITE_APP_NAME` - Application name
- `VITE_APP_VERSION` - Application version

### Optional Variables
- `VITE_ENABLE_DEV_TOOLS` - Enable development tools
- `VITE_USE_MOCK_DATA` - Use mock data for development
- `VITE_SEARCH_TIMEOUT` - Search timeout in milliseconds

## 🧪 Testing

Testing framework setup is planned with:
- **Unit Tests**: Jest + React Testing Library
- **Integration Tests**: API and component integration
- **E2E Tests**: Cypress or Playwright

```bash
# Run tests (when implemented)
npm test

# Run tests with coverage
npm run test:coverage
```

## 🚀 Deployment

### Production Build
```bash
npm run build
```

### Environment Setup
1. Set production environment variables
2. Configure API endpoints
3. Set up monitoring and analytics
4. Configure error tracking

## 🤝 Contributing

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Follow coding standards** outlined in PROJECT_RULES.md
4. **Commit changes**: `git commit -m 'feat: add amazing feature'`
5. **Push to branch**: `git push origin feature/amazing-feature`
6. **Open a Pull Request**

### Code Review Process
- All changes require code review
- Ensure tests pass and coverage is maintained
- Follow conventional commit messages
- Update documentation as needed

## 📝 License

This project is proprietary software. All rights reserved.

## 👥 Team

- **Development**: GEM Development Engineering
- **Contact**: <EMAIL>

## 🆘 Support

For support and questions:
1. Check the [documentation](docs/)
2. Search existing issues
3. Create a new issue with detailed description
4. Contact the development team

## 🔄 Changelog

### Version 1.0.0 (2025-01-08)
- Initial release
- Basic search functionality
- User authentication
- Responsive design
- CSV export capability

## 🎯 Roadmap

### Upcoming Features
- [ ] Advanced filtering options
- [ ] Real-time notifications
- [ ] Company comparison tools
- [ ] Performance analytics
- [ ] Mobile app version
- [ ] API integration with external services

### Technical Improvements
- [ ] Comprehensive test suite
- [ ] Performance optimization
- [ ] Accessibility enhancements
- [ ] Internationalization support
- [ ] Progressive Web App features

---

**Built with ❤️ by GEM Development Engineering**
