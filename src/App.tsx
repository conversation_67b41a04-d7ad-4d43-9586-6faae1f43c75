import React, { useState } from 'react';
import Navigation from './components/Navigation';
import LoginScreen from './components/LoginScreen';
import SearchCriteriaScreen from './components/SearchCriteriaScreen';
import ResultsScreen from './components/ResultsScreen';
import ErrorScreen from './components/ErrorScreen';
import { useAuth } from './hooks/useAuth';
import { useSearch } from './hooks/useSearch';
import { Screen } from './types';

function App() {
  const [currentScreen, setCurrentScreen] = useState<Screen>('login');
  const { user, isLoading: authLoading, error: authError, login, logout, isAuthenticated } = useAuth();
  const { results, search, cancelSearch, downloadCSV, resetSearch } = useSearch();

  const handleLogin = async (email: string, password: string) => {
    try {
      await login(email, password);
      setCurrentScreen('search');
    } catch (error) {
      // Error is handled by the useAuth hook
    }
  };

  const handleLogout = () => {
    logout();
    resetSearch();
    setCurrentScreen('login');
  };

  const handleSearch = async (criteria: any) => {
    setCurrentScreen('results');
    await search(criteria);
  };

  const handleNavigate = (screen: string) => {
    setCurrentScreen(screen as Screen);
  };

  const handleBackToSearch = () => {
    resetSearch();
    setCurrentScreen('search');
  };

  const handleRetry = () => {
    if (currentScreen === 'error') {
      setCurrentScreen('search');
    }
  };

  const handleCancel = () => {
    cancelSearch();
    setCurrentScreen('search');
  };

  // If there's a critical error, show error screen
  if (currentScreen === 'error' || (results.status === 'error' && currentScreen === 'results')) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation
          currentScreen={currentScreen}
          onNavigate={handleNavigate}
          onLogout={handleLogout}
          isLoggedIn={isAuthenticated}
        />
        <ErrorScreen
          error={results.error || 'An unexpected error occurred'}
          onRetry={handleRetry}
          onBackToHome={handleBackToSearch}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation
        currentScreen={currentScreen}
        onNavigate={handleNavigate}
        onLogout={handleLogout}
        isLoggedIn={isAuthenticated}
      />
      
      {!isAuthenticated ? (
        <LoginScreen onLogin={handleLogin} error={authError} />
      ) : (
        <>
          {currentScreen === 'search' && (
            <SearchCriteriaScreen
              onSearch={handleSearch}
              isLoading={authLoading}
            />
          )}
          
          {currentScreen === 'results' && (
            <ResultsScreen
              results={results}
              onCancel={handleCancel}
              onDownload={downloadCSV}
              onBackToSearch={handleBackToSearch}
            />
          )}
          
          {currentScreen === 'settings' && (
            <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
              <div className="max-w-2xl mx-auto">
                <div className="bg-white rounded-lg shadow-md p-8">
                  <h1 className="text-3xl font-bold text-gray-900 mb-4">Settings</h1>
                  <p className="text-gray-600">Settings functionality will be implemented here.</p>
                </div>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
}

export default App;