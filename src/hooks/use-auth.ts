import { useState, useCallback, useEffect } from 'react';
import { User } from '../types';
import { authService } from '../services/auth-service';
import { ERROR_MESSAGES } from '../constants/app-constants';

export const useAuth = () => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Check for existing authentication on mount
  useEffect(() => {
    const currentUser = authService.getCurrentUser();
    if (currentUser && authService.isAuthenticated()) {
      setUser(currentUser);
    }
  }, []);

  const login = useCallback(async (email: string, password: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await authService.login({ email, password });
      setUser(response.user);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : ERROR_MESSAGES.AUTHENTICATION_FAILED;
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const logout = useCallback(async () => {
    setIsLoading(true);
    try {
      await authService.logout();
      setUser(null);
      setError(null);
    } catch (err) {
      // Even if logout fails, clear local state
      setUser(null);
      setError(null);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const isAuthenticated = user !== null && authService.isAuthenticated();

  return {
    user,
    isLoading,
    error,
    login,
    logout,
    isAuthenticated
  };
};