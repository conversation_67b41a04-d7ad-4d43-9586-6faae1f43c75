import {
  validateEmail,
  validatePassword,
  validateForm,
  validateSearchCriteria,
} from '../../src/utils/validation-utils';

describe('validation-utils', () => {
  describe('validateEmail', () => {
    it('should return true for valid email addresses', () => {
      const validEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ];

      validEmails.forEach(email => {
        expect(validateEmail(email)).toBe(true);
      });
    });

    it('should return false for invalid email addresses', () => {
      const invalidEmails = [
        'invalid-email',
        '@example.com',
        'user@',
        'user@.com',
        '',
        'user <EMAIL>',
      ];

      invalidEmails.forEach(email => {
        expect(validateEmail(email)).toBe(false);
      });
    });
  });

  describe('validatePassword', () => {
    it('should return valid for strong passwords', () => {
      const strongPasswords = [
        'Password123',
        'MyStr0ngP@ssw0rd',
        'Test1234',
        'Abcd1234',
      ];

      strongPasswords.forEach(password => {
        const result = validatePassword(password);
        expect(result.isValid).toBe(true);
        expect(result.errors).toHaveLength(0);
      });
    });

    it('should return invalid for weak passwords with appropriate error messages', () => {
      const testCases = [
        {
          password: 'short',
          expectedErrors: [
            'Password must be at least 8 characters long',
            'Password must contain at least one uppercase letter',
            'Password must contain at least one number',
          ],
        },
        {
          password: 'nouppercase123',
          expectedErrors: [
            'Password must contain at least one uppercase letter',
          ],
        },
        {
          password: 'NOLOWERCASE123',
          expectedErrors: [
            'Password must contain at least one lowercase letter',
          ],
        },
        {
          password: 'NoNumbers',
          expectedErrors: [
            'Password must contain at least one number',
          ],
        },
      ];

      testCases.forEach(({ password, expectedErrors }) => {
        const result = validatePassword(password);
        expect(result.isValid).toBe(false);
        expect(result.errors).toEqual(expect.arrayContaining(expectedErrors));
      });
    });
  });

  describe('validateForm', () => {
    it('should return valid for complete form data', () => {
      const formData = {
        email: '<EMAIL>',
        password: 'Password123',
        name: 'John Doe',
      };

      const result = validateForm(formData);
      expect(result.isValid).toBe(true);
      expect(Object.keys(result.errors)).toHaveLength(0);
    });

    it('should return invalid for incomplete form data', () => {
      const formData = {
        email: '',
        password: 'Password123',
        name: '',
      };

      const result = validateForm(formData);
      expect(result.isValid).toBe(false);
      expect(result.errors.email).toBe('Email is required');
      expect(result.errors.name).toBe('Name is required');
      expect(result.errors.password).toBeUndefined();
    });

    it('should handle null and undefined values', () => {
      const formData = {
        email: null,
        password: undefined,
        name: 'John Doe',
      };

      const result = validateForm(formData);
      expect(result.isValid).toBe(false);
      expect(result.errors.email).toBe('Email is required');
      expect(result.errors.password).toBe('Password is required');
      expect(result.errors.name).toBeUndefined();
    });
  });

  describe('validateSearchCriteria', () => {
    it('should return valid for correct search criteria', () => {
      const validCriteria = [
        { commissionRate: 0 },
        { commissionRate: 50 },
        { commissionRate: 100 },
        { commissionRate: 25.5 },
      ];

      validCriteria.forEach(criteria => {
        const result = validateSearchCriteria(criteria);
        expect(result.isValid).toBe(true);
        expect(result.errors).toHaveLength(0);
      });
    });

    it('should return invalid for incorrect commission rates', () => {
      const invalidCriteria = [
        { commissionRate: -1 },
        { commissionRate: 101 },
        { commissionRate: 'invalid' },
        { commissionRate: null },
        { commissionRate: undefined },
      ];

      invalidCriteria.forEach(criteria => {
        const result = validateSearchCriteria(criteria);
        expect(result.isValid).toBe(false);
        expect(result.errors).toContain('Commission rate must be between 0 and 100');
      });
    });
  });
});
