import React from 'react';
import { Menu, X, Search, Settings, LogOut } from 'lucide-react';

interface NavigationProps {
  currentScreen: string;
  onNavigate: (screen: string) => void;
  onLogout: () => void;
  isLoggedIn: boolean;
}

const Navigation: React.FC<NavigationProps> = ({ 
  currentScreen, 
  onNavigate, 
  onLogout,
  isLoggedIn 
}) => {
  const [isMenuOpen, setIsMenuOpen] = React.useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const handleNavigate = (screen: string) => {
    onNavigate(screen);
    setIsMenuOpen(false);
  };

  if (!isLoggedIn) {
    return null;
  }

  return (
    <nav className="bg-gray-900 text-white shadow-lg">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex-shrink-0">
            <h1 className="text-xl font-bold">COMCROW_CSA</h1>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:block">
            <div className="ml-10 flex items-baseline space-x-4">
              <button
                onClick={() => handleNavigate('search')}
                className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  currentScreen === 'search'
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                }`}
                aria-label="Search opportunities"
              >
                <Search className="inline w-4 h-4 mr-2" />
                Search
              </button>
              <button
                onClick={() => handleNavigate('settings')}
                className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  currentScreen === 'settings'
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                }`}
                aria-label="Settings"
              >
                <Settings className="inline w-4 h-4 mr-2" />
                Settings
              </button>
              <button
                onClick={onLogout}
                className="text-gray-300 hover:bg-gray-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium transition-colors"
                aria-label="Logout"
              >
                <LogOut className="inline w-4 h-4 mr-2" />
                Logout
              </button>
            </div>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={toggleMenu}
              className="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
              aria-expanded="false"
              aria-label="Toggle navigation menu"
            >
              {isMenuOpen ? (
                <X className="block h-6 w-6" />
              ) : (
                <Menu className="block h-6 w-6" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isMenuOpen && (
        <div className="md:hidden">
          <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-gray-800">
            <button
              onClick={() => handleNavigate('search')}
              className={`block px-3 py-2 rounded-md text-base font-medium w-full text-left transition-colors ${
                currentScreen === 'search'
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-300 hover:bg-gray-700 hover:text-white'
              }`}
            >
              <Search className="inline w-4 h-4 mr-2" />
              Search
            </button>
            <button
              onClick={() => handleNavigate('settings')}
              className={`block px-3 py-2 rounded-md text-base font-medium w-full text-left transition-colors ${
                currentScreen === 'settings'
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-300 hover:bg-gray-700 hover:text-white'
              }`}
            >
              <Settings className="inline w-4 h-4 mr-2" />
              Settings
            </button>
            <button
              onClick={onLogout}
              className="block text-gray-300 hover:bg-gray-700 hover:text-white px-3 py-2 rounded-md text-base font-medium w-full text-left transition-colors"
            >
              <LogOut className="inline w-4 h-4 mr-2" />
              Logout
            </button>
          </div>
        </div>
      )}
    </nav>
  );
};

export default Navigation;