import { useState, useCallback } from 'react';
import { SearchCriteria, SearchResults, Company } from '../types';

// Mock data for demonstration
const mockCompanies: Company[] = [
  {
    id: '1',
    name: 'TechSolutions Inc.',
    description: 'Leading B2B software solutions for enterprise clients',
    commissionRate: 25,
    type: 'B2B',
    language: 'English',
    location: 'Global',
    experienceRequired: false
  },
  {
    id: '2',
    name: 'CloudServices Pro',
    description: 'Cloud infrastructure and hosting services',
    commissionRate: 30,
    type: 'B2B',
    language: 'English',
    location: 'USA',
    experienceRequired: false
  },
  {
    id: '3',
    name: 'Marketing Dynamics',
    description: 'Digital marketing and advertising solutions',
    commissionRate: 22,
    type: 'B2B',
    language: 'English',
    location: 'Global',
    experienceRequired: false
  },
  {
    id: '4',
    name: 'Financial Partners',
    description: 'Business lending and financial services',
    commissionRate: 35,
    type: 'B2B',
    language: 'English',
    location: 'UK',
    experienceRequired: true
  },
  {
    id: '5',
    name: 'HR Solutions Group',
    description: 'Human resources and talent management',
    commissionRate: 28,
    type: 'B2B',
    language: 'English',
    location: 'Global',
    experienceRequired: false
  }
];

export const useSearch = () => {
  const [results, setResults] = useState<SearchResults>({
    companies: [],
    totalCount: 0,
    status: 'idle',
    progress: 0
  });

  const search = useCallback(async (criteria: SearchCriteria) => {
    setResults(prev => ({ ...prev, status: 'searching', progress: 0, error: undefined }));

    try {
      // Simulate search progress
      const steps = [
        { status: 'searching' as const, progress: 20, delay: 800 },
        { status: 'searching' as const, progress: 50, delay: 1000 },
        { status: 'extracting' as const, progress: 75, delay: 1200 },
        { status: 'extracting' as const, progress: 90, delay: 800 },
        { status: 'completed' as const, progress: 100, delay: 500 }
      ];

      for (const step of steps) {
        await new Promise(resolve => setTimeout(resolve, step.delay));
        setResults(prev => ({ 
          ...prev, 
          status: step.status, 
          progress: step.progress 
        }));
      }

      // Filter mock companies based on criteria
      let filteredCompanies = mockCompanies;

      if (criteria.b2b) {
        filteredCompanies = filteredCompanies.filter(c => c.type === 'B2B');
      }

      if (criteria.english) {
        filteredCompanies = filteredCompanies.filter(c => c.language === 'English');
      }

      if (criteria.global) {
        filteredCompanies = filteredCompanies.filter(c => c.location === 'Global');
      }

      if (criteria.noExperience) {
        filteredCompanies = filteredCompanies.filter(c => !c.experienceRequired);
      }

      if (criteria.commissionRate > 0) {
        filteredCompanies = filteredCompanies.filter(c => c.commissionRate >= criteria.commissionRate);
      }

      // Add some randomness to simulate real search variability
      const randomCount = Math.floor(Math.random() * 50) + filteredCompanies.length;
      
      setResults({
        companies: filteredCompanies,
        totalCount: randomCount,
        status: 'completed',
        progress: 100
      });

    } catch (error) {
      setResults(prev => ({
        ...prev,
        status: 'error',
        error: error instanceof Error ? error.message : 'Search failed'
      }));
    }
  }, []);

  const cancelSearch = useCallback(() => {
    setResults(prev => ({ ...prev, status: 'idle', progress: 0 }));
  }, []);

  const downloadCSV = useCallback(() => {
    // Generate CSV content
    const csvHeaders = ['Company Name', 'Description', 'Commission Rate', 'Type', 'Language', 'Location', 'Experience Required'];
    const csvRows = results.companies.map(company => [
      company.name,
      company.description,
      `${company.commissionRate}%`,
      company.type,
      company.language,
      company.location,
      company.experienceRequired ? 'Yes' : 'No'
    ]);

    const csvContent = [csvHeaders, ...csvRows]
      .map(row => row.map(cell => `"${cell}"`).join(','))
      .join('\n');

    // Create and download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `comcrow_opportunities_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }, [results.companies]);

  const resetSearch = useCallback(() => {
    setResults({
      companies: [],
      totalCount: 0,
      status: 'idle',
      progress: 0
    });
  }, []);

  return {
    results,
    search,
    cancelSearch,
    downloadCSV,
    resetSearch
  };
};