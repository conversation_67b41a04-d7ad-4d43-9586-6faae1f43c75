# COMCROW CSA - Project Rules and Guidelines

This document establishes the coding standards, development workflow, and project guidelines for the COMCROW CSA application.

## Table of Contents

1. [Coding Standards](#coding-standards)
2. [File Naming Conventions](#file-naming-conventions)
3. [Directory Organization](#directory-organization)
4. [Development Workflow](#development-workflow)
5. [Code Review Guidelines](#code-review-guidelines)
6. [Testing Requirements](#testing-requirements)
7. [Documentation Standards](#documentation-standards)
8. [Performance Guidelines](#performance-guidelines)
9. [Security Guidelines](#security-guidelines)
10. [Git Workflow](#git-workflow)

## Coding Standards

### TypeScript/JavaScript

#### General Rules
- Use TypeScript for all new code
- Enable strict mode in TypeScript configuration
- Prefer `const` over `let`, avoid `var`
- Use meaningful variable and function names
- Write self-documenting code with clear intent

#### Formatting
- Use Prettier for code formatting
- 2 spaces for indentation
- Single quotes for strings
- Trailing commas in multiline structures
- Semicolons required

#### Example:
```typescript
// Good
const getUserData = async (userId: string): Promise<User> => {
  const response = await apiService.get<User>(`/users/${userId}`);
  return response;
};

// Bad
var userData = function(id) {
  return fetch('/users/' + id).then(r => r.json())
}
```

### React Components

#### Component Structure
```typescript
// 1. Imports (external libraries first, then internal)
import React, { useState, useEffect } from 'react';
import { User } from '../types';

// 2. Interface definitions
interface ComponentProps {
  user: User;
  onUpdate: (user: User) => void;
}

// 3. Component implementation
const ComponentName: React.FC<ComponentProps> = ({ user, onUpdate }) => {
  // 4. State and hooks
  const [isLoading, setIsLoading] = useState(false);
  
  // 5. Event handlers
  const handleUpdate = () => {
    // Implementation
  };
  
  // 6. Effects
  useEffect(() => {
    // Implementation
  }, []);
  
  // 7. Render
  return (
    <div>
      {/* JSX */}
    </div>
  );
};

// 8. Export
export default ComponentName;
```

#### Component Guidelines
- Use functional components with hooks
- Keep components small and focused (< 200 lines)
- Extract complex logic into custom hooks
- Use TypeScript interfaces for props
- Implement proper error boundaries

### CSS/Styling

#### Tailwind CSS Usage
- Use Tailwind utility classes for styling
- Create custom components for repeated patterns
- Use CSS modules for component-specific styles when needed
- Follow mobile-first responsive design

#### Class Naming
```typescript
// Good
<div className="flex items-center justify-between p-4 bg-white rounded-lg shadow-md">

// Bad
<div className="container">
```

## File Naming Conventions

### General Rules
- Use lowercase with hyphens (kebab-case) for files
- Use descriptive, self-explanatory names
- Include file type in name when appropriate

### Specific Conventions
| File Type | Convention | Example |
|-----------|------------|---------|
| React Components | `component-name.tsx` | `user-profile.tsx` |
| Hooks | `use-hook-name.ts` | `use-auth.ts` |
| Services | `service-name-service.ts` | `auth-service.ts` |
| Utilities | `utility-name-utils.ts` | `validation-utils.ts` |
| Types | `type-name.ts` | `user-types.ts` |
| Constants | `constant-name-constants.ts` | `app-constants.ts` |
| Tests | `file-name.test.ts` | `auth-service.test.ts` |

## Directory Organization

### Mandatory Structure
- Follow the directory structure defined in `DIRECTORY_STRUCTURE.md`
- Place files in appropriate directories based on their purpose
- Do not create new top-level directories without team approval

### Component Organization
```
src/components/
├── forms/          # Form components
├── layout/         # Layout and navigation
└── ui/            # Display components
```

### Service Organization
```
src/services/
├── api-service.ts     # Base API service
├── auth-service.ts    # Authentication
└── feature-service.ts # Feature-specific services
```

## Development Workflow

### Before Starting Work
1. Pull latest changes from main branch
2. Create a feature branch: `git checkout -b feature/feature-name`
3. Ensure all dependencies are installed: `npm install`
4. Run tests to ensure everything works: `npm test`

### During Development
1. Write code following established standards
2. Add tests for new functionality
3. Update documentation as needed
4. Commit changes frequently with descriptive messages

### Before Submitting
1. Run linting: `npm run lint`
2. Run tests: `npm test`
3. Build project: `npm run build`
4. Update documentation if needed
5. Create pull request with detailed description

## Code Review Guidelines

### For Authors
- Provide clear PR description with context
- Include screenshots for UI changes
- Ensure all tests pass
- Self-review code before submitting
- Respond promptly to feedback

### For Reviewers
- Review within 24 hours
- Focus on logic, performance, and maintainability
- Provide constructive feedback
- Approve only when confident in changes
- Check that tests cover new functionality

### Review Checklist
- [ ] Code follows project standards
- [ ] Tests are included and passing
- [ ] Documentation is updated
- [ ] No security vulnerabilities
- [ ] Performance considerations addressed
- [ ] Error handling implemented
- [ ] Accessibility requirements met

## Testing Requirements

### Unit Tests
- Write tests for all utility functions
- Test React components with React Testing Library
- Achieve minimum 80% code coverage
- Mock external dependencies

### Integration Tests
- Test service integrations
- Test component interactions
- Test API endpoints

### Test Structure
```typescript
describe('ComponentName', () => {
  beforeEach(() => {
    // Setup
  });

  it('should render correctly', () => {
    // Test implementation
  });

  it('should handle user interaction', () => {
    // Test implementation
  });
});
```

## Documentation Standards

### Code Documentation
- Use JSDoc comments for functions and classes
- Document complex algorithms and business logic
- Include examples for utility functions
- Keep comments up-to-date with code changes

### README Updates
- Update README.md for significant changes
- Include setup instructions for new dependencies
- Document new environment variables
- Update API documentation

### Inline Comments
```typescript
// Good: Explains why, not what
// Retry failed requests up to 3 times to handle network issues
const retryRequest = async (request: () => Promise<any>, maxRetries = 3) => {
  // Implementation
};

// Bad: Explains what the code does (obvious)
// Increment counter by 1
counter++;
```

## Performance Guidelines

### React Performance
- Use React.memo for expensive components
- Implement proper dependency arrays in useEffect
- Avoid creating objects/functions in render
- Use lazy loading for large components

### Bundle Size
- Monitor bundle size with each build
- Use dynamic imports for code splitting
- Remove unused dependencies regularly
- Optimize images and assets

### API Performance
- Implement request caching where appropriate
- Use pagination for large data sets
- Implement proper loading states
- Handle errors gracefully

## Security Guidelines

### Data Handling
- Never store sensitive data in localStorage
- Validate all user inputs
- Sanitize data before display
- Use HTTPS for all API calls

### Authentication
- Implement proper token management
- Use secure session handling
- Implement proper logout functionality
- Handle token expiration gracefully

### Environment Variables
- Never commit sensitive data to version control
- Use environment variables for configuration
- Validate environment variables on startup
- Document all required environment variables

## Git Workflow

### Branch Naming
- `feature/feature-name` - New features
- `bugfix/bug-description` - Bug fixes
- `hotfix/critical-fix` - Critical production fixes
- `refactor/component-name` - Code refactoring

### Commit Messages
Follow conventional commit format:
```
type(scope): description

[optional body]

[optional footer]
```

Types: `feat`, `fix`, `docs`, `style`, `refactor`, `test`, `chore`

Examples:
```
feat(auth): add password reset functionality
fix(search): resolve pagination issue
docs(readme): update installation instructions
```

### Pull Request Process
1. Create descriptive PR title
2. Fill out PR template completely
3. Request review from team members
4. Address all feedback before merging
5. Squash commits when merging

## Enforcement

### Automated Checks
- ESLint for code quality
- Prettier for code formatting
- TypeScript for type checking
- Jest for testing
- Husky for pre-commit hooks

### Manual Reviews
- All code must be reviewed before merging
- Documentation updates require review
- Breaking changes require team discussion
- Performance impacts require measurement

## Exceptions

Exceptions to these rules must be:
1. Documented in code comments
2. Approved by team lead
3. Temporary with plan for resolution
4. Justified by specific requirements

## Updates

This document should be updated when:
- New tools are adopted
- Standards change
- Team grows or changes
- Project requirements evolve

Last updated: 2025-01-08
