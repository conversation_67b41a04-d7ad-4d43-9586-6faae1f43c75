import React from 'react';
import { Al<PERSON><PERSON>riangle, RefreshCw, Home } from 'lucide-react';

interface ErrorScreenProps {
  error: string;
  onRetry: () => void;
  onBackToHome: () => void;
}

const ErrorScreen: React.FC<ErrorScreenProps> = ({
  error,
  onRetry,
  onBackToHome
}) => {
  const getErrorDetails = (error: string) => {
    switch (error) {
      case 'INVALID_CREDENTIALS':
        return {
          title: 'Invalid Credentials',
          message: 'Your CommissionCrowd credentials are invalid or have expired. Please check your login information.',
          suggestion: 'Try logging in again or contact support if the problem persists.'
        };
      case 'NETWORK_ERROR':
        return {
          title: 'Network Connection Error',
          message: 'Unable to connect to the server. Please check your internet connection.',
          suggestion: 'Check your internet connection and try again.'
        };
      case 'SEARCH_FAILED':
        return {
          title: 'Search Failed',
          message: 'The search process encountered an error and could not complete.',
          suggestion: 'Try adjusting your search criteria or wait a moment before trying again.'
        };
      case 'RATE_LIMIT_EXCEEDED':
        return {
          title: 'Rate Limit Exceeded',
          message: 'You have exceeded the maximum number of searches allowed. Please wait before trying again.',
          suggestion: 'Wait a few minutes before performing another search.'
        };
      default:
        return {
          title: 'Unexpected Error',
          message: error || 'An unexpected error occurred while processing your request.',
          suggestion: 'Please try again. If the problem persists, contact support.'
        };
    }
  };

  const errorDetails = getErrorDetails(error);

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md mx-auto">
        <div className="text-center mb-8">
          <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-4">
            <AlertTriangle className="h-8 w-8 text-red-600" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            {errorDetails.title}
          </h1>
          <p className="text-lg text-gray-600">
            Something went wrong
          </p>
        </div>

        <div className="bg-white rounded-lg shadow-md p-8">
          <div className="text-center">
            <div 
              className="bg-red-50 border border-red-200 rounded-lg p-6 mb-6"
              role="alert"
              aria-live="polite"
            >
              <h2 className="text-lg font-semibold text-red-800 mb-2">
                Error Details
              </h2>
              <p className="text-red-700 mb-4">
                {errorDetails.message}
              </p>
              <p className="text-sm text-red-600">
                {errorDetails.suggestion}
              </p>
            </div>

            <div className="space-y-4">
              <button
                onClick={onRetry}
                className="w-full flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
              >
                <RefreshCw className="h-5 w-5 mr-2" />
                Try Again
              </button>

              <button
                onClick={onBackToHome}
                className="w-full flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
              >
                <Home className="h-5 w-5 mr-2" />
                Back to Search
              </button>
            </div>
          </div>
        </div>

        {/* Additional Help */}
        <div className="mt-8 text-center">
          <p className="text-sm text-gray-500">
            Need help? Contact our support team at{' '}
            <a
              href="mailto:<EMAIL>"
              className="text-blue-600 hover:text-blue-500 transition-colors"
            >
              <EMAIL>
            </a>
          </p>
        </div>
      </div>
    </div>
  );
};

export default ErrorScreen;