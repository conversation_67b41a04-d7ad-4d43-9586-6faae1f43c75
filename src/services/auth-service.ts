import { apiService } from './api-service';
import { AUTH_CONFIG, ERROR_MESSAGES } from '../constants/app-constants';
import { User } from '../types';

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface AuthResponse {
  user: User;
  token: string;
  refreshToken: string;
}

export class AuthService {
  // Login user
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      // For now, simulate API call with mock data
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock validation - replace with actual API call
      if (credentials.email === '<EMAIL>' && credentials.password === 'password123') {
        const mockResponse: AuthResponse = {
          user: {
            id: '1',
            email: credentials.email,
            name: 'Demo User'
          },
          token: 'mock-jwt-token',
          refreshToken: 'mock-refresh-token'
        };
        
        // Store tokens in localStorage
        this.storeTokens(mockResponse.token, mockResponse.refreshToken);
        this.storeUser(mockResponse.user);
        
        return mockResponse;
      } else {
        throw new Error(ERROR_MESSAGES.INVALID_CREDENTIALS);
      }
      
      // Actual implementation would be:
      // return await apiService.post<AuthResponse>('/auth/login', credentials);
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(ERROR_MESSAGES.AUTHENTICATION_FAILED);
    }
  }

  // Logout user
  async logout(): Promise<void> {
    try {
      // Clear stored data
      this.clearStoredData();
      
      // In a real implementation, you might want to invalidate the token on the server
      // await apiService.post('/auth/logout');
    } catch (error) {
      // Even if logout fails on server, clear local data
      this.clearStoredData();
    }
  }

  // Check if user is authenticated
  isAuthenticated(): boolean {
    const token = this.getStoredToken();
    const user = this.getStoredUser();
    return !!(token && user);
  }

  // Get current user
  getCurrentUser(): User | null {
    return this.getStoredUser();
  }

  // Refresh token
  async refreshToken(): Promise<string> {
    const refreshToken = this.getStoredRefreshToken();
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    try {
      // Mock implementation - replace with actual API call
      const response = await apiService.post<{ token: string }>('/auth/refresh', {
        refreshToken
      });
      
      this.storeTokens(response.token, refreshToken);
      return response.token;
    } catch (error) {
      this.clearStoredData();
      throw new Error(ERROR_MESSAGES.SESSION_EXPIRED);
    }
  }

  // Private methods for token management
  private storeTokens(token: string, refreshToken: string): void {
    localStorage.setItem(AUTH_CONFIG.TOKEN_KEY, token);
    localStorage.setItem(AUTH_CONFIG.REFRESH_TOKEN_KEY, refreshToken);
  }

  private storeUser(user: User): void {
    localStorage.setItem(AUTH_CONFIG.USER_KEY, JSON.stringify(user));
  }

  private getStoredToken(): string | null {
    return localStorage.getItem(AUTH_CONFIG.TOKEN_KEY);
  }

  private getStoredRefreshToken(): string | null {
    return localStorage.getItem(AUTH_CONFIG.REFRESH_TOKEN_KEY);
  }

  private getStoredUser(): User | null {
    const userStr = localStorage.getItem(AUTH_CONFIG.USER_KEY);
    if (!userStr) return null;
    
    try {
      return JSON.parse(userStr);
    } catch {
      return null;
    }
  }

  private clearStoredData(): void {
    localStorage.removeItem(AUTH_CONFIG.TOKEN_KEY);
    localStorage.removeItem(AUTH_CONFIG.REFRESH_TOKEN_KEY);
    localStorage.removeItem(AUTH_CONFIG.USER_KEY);
  }
}

// Create singleton instance
export const authService = new AuthService();
