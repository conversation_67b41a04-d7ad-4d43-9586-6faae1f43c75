// Application-wide constants
export const APP_CONFIG = {
  NAME: 'COMCROW CSA',
  FULL_NAME: 'COMCROW Professional Sales Opportunity Search Application',
  VERSION: '1.0.0',
  DESCRIPTION: 'Professional Sales Opportunity Search Application for finding commission-based sales opportunities',
} as const;

// API Configuration
export const API_CONFIG = {
  BASE_URL: process.env.VITE_API_BASE_URL || 'https://api.comcrow.com',
  TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
} as const;

// Authentication Constants
export const AUTH_CONFIG = {
  TOKEN_KEY: 'comcrow_auth_token',
  REFRESH_TOKEN_KEY: 'comcrow_refresh_token',
  USER_KEY: 'comcrow_user',
  SESSION_TIMEOUT: 24 * 60 * 60 * 1000, // 24 hours
} as const;

// Search Configuration
export const SEARCH_CONFIG = {
  MAX_RESULTS: 1000,
  DEFAULT_PAGE_SIZE: 50,
  MIN_COMMISSION_RATE: 0,
  MAX_COMMISSION_RATE: 100,
  SEARCH_TIMEOUT: 60000, // 1 minute
} as const;

// UI Constants
export const UI_CONFIG = {
  DEBOUNCE_DELAY: 300,
  ANIMATION_DURATION: 200,
  TOAST_DURATION: 5000,
} as const;

// Screen Names
export const SCREENS = {
  LOGIN: 'login',
  SEARCH: 'search',
  RESULTS: 'results',
  SETTINGS: 'settings',
  ERROR: 'error',
} as const;

// Company Types
export const COMPANY_TYPES = {
  B2B: 'B2B',
  B2C: 'B2C',
} as const;

// Search Status
export const SEARCH_STATUS = {
  IDLE: 'idle',
  SEARCHING: 'searching',
  EXTRACTING: 'extracting',
  COMPLETED: 'completed',
  ERROR: 'error',
} as const;

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error. Please check your connection and try again.',
  AUTHENTICATION_FAILED: 'Authentication failed. Please check your credentials.',
  SEARCH_FAILED: 'Search failed. Please try again.',
  INVALID_CREDENTIALS: 'Invalid email or password',
  SESSION_EXPIRED: 'Your session has expired. Please log in again.',
  UNKNOWN_ERROR: 'An unexpected error occurred. Please try again.',
} as const;

// Success Messages
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: 'Successfully logged in',
  SEARCH_COMPLETED: 'Search completed successfully',
  DATA_EXPORTED: 'Data exported successfully',
} as const;
