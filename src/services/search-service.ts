import { apiService } from './api-service';
import { SEARCH_CONFIG, ERROR_MESSAGES } from '../constants/app-constants';
import { SearchCriteria, Company, SearchResults } from '../types';

// Mock data for demonstration
const mockCompanies: Company[] = [
  {
    id: '1',
    name: 'TechSolutions Inc.',
    description: 'Leading B2B software solutions for enterprise clients',
    commissionRate: 25,
    type: 'B2B',
    language: 'English',
    location: 'Global',
    experienceRequired: false
  },
  {
    id: '2',
    name: 'CloudServices Pro',
    description: 'Cloud infrastructure and hosting services',
    commissionRate: 30,
    type: 'B2B',
    language: 'English',
    location: 'USA',
    experienceRequired: false
  },
  {
    id: '3',
    name: 'Marketing Dynamics',
    description: 'Digital marketing and advertising solutions',
    commissionRate: 22,
    type: 'B2B',
    language: 'English',
    location: 'Global',
    experienceRequired: true
  },
  {
    id: '4',
    name: 'E-commerce Plus',
    description: 'E-commerce platform and online retail solutions',
    commissionRate: 18,
    type: 'B2C',
    language: 'English',
    location: 'Europe',
    experienceRequired: false
  },
  {
    id: '5',
    name: 'FinTech Innovations',
    description: 'Financial technology and payment processing',
    commissionRate: 35,
    type: 'B2B',
    language: 'English',
    location: 'Global',
    experienceRequired: true
  }
];

export class SearchService {
  private abortController: AbortController | null = null;

  // Perform search based on criteria
  async search(
    criteria: SearchCriteria,
    onProgress?: (progress: number, status: string) => void
  ): Promise<Company[]> {
    try {
      // Cancel any existing search
      this.cancelSearch();
      
      // Create new abort controller
      this.abortController = new AbortController();

      // Simulate search progress
      onProgress?.(10, 'Initializing search...');
      await this.delay(500);
      
      if (this.abortController.signal.aborted) {
        throw new Error('Search cancelled');
      }

      onProgress?.(30, 'Searching companies...');
      await this.delay(1000);
      
      if (this.abortController.signal.aborted) {
        throw new Error('Search cancelled');
      }

      onProgress?.(60, 'Filtering results...');
      await this.delay(800);
      
      if (this.abortController.signal.aborted) {
        throw new Error('Search cancelled');
      }

      onProgress?.(80, 'Extracting data...');
      await this.delay(600);
      
      if (this.abortController.signal.aborted) {
        throw new Error('Search cancelled');
      }

      // Filter mock data based on criteria
      const filteredCompanies = this.filterCompanies(mockCompanies, criteria);
      
      onProgress?.(100, 'Search completed');
      
      // In a real implementation, this would be:
      // return await apiService.post<Company[]>('/search', criteria);
      
      return filteredCompanies;
    } catch (error) {
      if (error instanceof Error && error.message === 'Search cancelled') {
        throw error;
      }
      throw new Error(ERROR_MESSAGES.SEARCH_FAILED);
    } finally {
      this.abortController = null;
    }
  }

  // Cancel ongoing search
  cancelSearch(): void {
    if (this.abortController) {
      this.abortController.abort();
      this.abortController = null;
    }
  }

  // Export search results to CSV
  async exportToCSV(companies: Company[]): Promise<string> {
    try {
      const headers = [
        'Company Name',
        'Description',
        'Commission Rate (%)',
        'Type',
        'Language',
        'Location',
        'Experience Required'
      ];

      const csvContent = [
        headers.join(','),
        ...companies.map(company => [
          `"${company.name}"`,
          `"${company.description}"`,
          company.commissionRate.toString(),
          company.type,
          company.language,
          company.location,
          company.experienceRequired ? 'Yes' : 'No'
        ].join(','))
      ].join('\n');

      return csvContent;
    } catch (error) {
      throw new Error('Failed to export data');
    }
  }

  // Private helper methods
  private filterCompanies(companies: Company[], criteria: SearchCriteria): Company[] {
    return companies.filter(company => {
      // Filter by B2B/B2C
      if (criteria.b2b && company.type !== 'B2B') return false;
      if (!criteria.b2b && company.type === 'B2B') return false;

      // Filter by English language
      if (criteria.english && company.language !== 'English') return false;

      // Filter by global location
      if (criteria.global && company.location !== 'Global') return false;

      // Filter by experience requirement
      if (criteria.noExperience && company.experienceRequired) return false;

      // Filter by commission rate
      if (company.commissionRate < criteria.commissionRate) return false;

      return true;
    });
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Create singleton instance
export const searchService = new SearchService();
