import { ApiService } from '../../src/services/api-service';

// Mock fetch globally
const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;

describe('ApiService', () => {
  let apiService: ApiService;

  beforeEach(() => {
    apiService = new ApiService();
    jest.clearAllMocks();
  });

  describe('GET requests', () => {
    it('should make successful GET request', async () => {
      const mockResponse = { data: 'test' };
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      } as Response);

      const result = await apiService.get('/test-endpoint');

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/test-endpoint',
        expect.objectContaining({
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        })
      );
      expect(result).toEqual(mockResponse);
    });

    it('should include custom headers in GET request', async () => {
      const mockResponse = { data: 'test' };
      const customHeaders = { Authorization: 'Bearer token' };
      
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      } as Response);

      await apiService.get('/test-endpoint', customHeaders);

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/test-endpoint',
        expect.objectContaining({
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            Authorization: 'Bearer token',
          },
        })
      );
    });

    it('should handle HTTP errors', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
      } as Response);

      await expect(apiService.get('/not-found')).rejects.toThrow('HTTP error! status: 404');
    });

    it('should handle network errors', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      await expect(apiService.get('/test-endpoint')).rejects.toThrow('Network error');
    });

    it('should handle request timeout', async () => {
      // Mock AbortError for timeout simulation
      const abortError = new Error('Request timeout');
      abortError.name = 'AbortError';
      mockFetch.mockRejectedValueOnce(abortError);

      await expect(apiService.get('/test-endpoint')).rejects.toThrow('Request timeout');
    });
  });

  describe('POST requests', () => {
    it('should make successful POST request with data', async () => {
      const mockResponse = { success: true };
      const postData = { name: 'test', value: 123 };
      
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      } as Response);

      const result = await apiService.post('/test-endpoint', postData);

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/test-endpoint',
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify(postData),
          headers: {
            'Content-Type': 'application/json',
          },
        })
      );
      expect(result).toEqual(mockResponse);
    });

    it('should make POST request without data', async () => {
      const mockResponse = { success: true };
      
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      } as Response);

      await apiService.post('/test-endpoint');

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/test-endpoint',
        expect.objectContaining({
          method: 'POST',
          body: undefined,
          headers: {
            'Content-Type': 'application/json',
          },
        })
      );
    });
  });

  describe('PUT requests', () => {
    it('should make successful PUT request', async () => {
      const mockResponse = { updated: true };
      const putData = { id: 1, name: 'updated' };
      
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      } as Response);

      const result = await apiService.put('/test-endpoint', putData);

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/test-endpoint',
        expect.objectContaining({
          method: 'PUT',
          body: JSON.stringify(putData),
        })
      );
      expect(result).toEqual(mockResponse);
    });
  });

  describe('DELETE requests', () => {
    it('should make successful DELETE request', async () => {
      const mockResponse = { deleted: true };
      
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      } as Response);

      const result = await apiService.delete('/test-endpoint');

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/test-endpoint',
        expect.objectContaining({
          method: 'DELETE',
        })
      );
      expect(result).toEqual(mockResponse);
    });
  });
});
