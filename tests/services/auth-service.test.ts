import { AuthService } from '../../src/services/auth-service';
import { AUTH_CONFIG } from '../../src/constants/app-constants';

describe('AuthService', () => {
  let authService: AuthService;
  let mockLocalStorage: { [key: string]: string };

  beforeEach(() => {
    authService = new AuthService();
    mockLocalStorage = {};
    
    // Mock localStorage
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: jest.fn((key: string) => mockLocalStorage[key] || null),
        setItem: jest.fn((key: string, value: string) => {
          mockLocalStorage[key] = value;
        }),
        removeItem: jest.fn((key: string) => {
          delete mockLocalStorage[key];
        }),
        clear: jest.fn(() => {
          mockLocalStorage = {};
        }),
      },
      writable: true,
    });
  });

  describe('login', () => {
    it('should login successfully with valid credentials', async () => {
      const credentials = {
        email: '<EMAIL>',
        password: 'password123',
      };

      const result = await authService.login(credentials);

      expect(result.user.email).toBe(credentials.email);
      expect(result.user.name).toBe('Demo User');
      expect(result.token).toBe('mock-jwt-token');
      expect(result.refreshToken).toBe('mock-refresh-token');

      // Check that tokens are stored
      expect(localStorage.setItem).toHaveBeenCalledWith(
        AUTH_CONFIG.TOKEN_KEY,
        'mock-jwt-token'
      );
      expect(localStorage.setItem).toHaveBeenCalledWith(
        AUTH_CONFIG.REFRESH_TOKEN_KEY,
        'mock-refresh-token'
      );
      expect(localStorage.setItem).toHaveBeenCalledWith(
        AUTH_CONFIG.USER_KEY,
        JSON.stringify(result.user)
      );
    });

    it('should reject login with invalid credentials', async () => {
      const credentials = {
        email: '<EMAIL>',
        password: 'wrongpassword',
      };

      await expect(authService.login(credentials)).rejects.toThrow(
        'Invalid email or password'
      );

      // Check that no tokens are stored
      expect(localStorage.setItem).not.toHaveBeenCalled();
    });

    it('should handle login timeout', async () => {
      // This test verifies the login process includes a delay
      const credentials = {
        email: '<EMAIL>',
        password: 'password123',
      };

      const startTime = Date.now();
      await authService.login(credentials);
      const endTime = Date.now();

      // Should take at least 1000ms due to the simulated delay
      expect(endTime - startTime).toBeGreaterThanOrEqual(1000);
    });
  });

  describe('logout', () => {
    beforeEach(() => {
      // Set up some stored data
      mockLocalStorage[AUTH_CONFIG.TOKEN_KEY] = 'test-token';
      mockLocalStorage[AUTH_CONFIG.REFRESH_TOKEN_KEY] = 'test-refresh-token';
      mockLocalStorage[AUTH_CONFIG.USER_KEY] = JSON.stringify({
        id: '1',
        email: '<EMAIL>',
        name: 'Test User',
      });
    });

    it('should clear stored data on logout', async () => {
      await authService.logout();

      expect(localStorage.removeItem).toHaveBeenCalledWith(AUTH_CONFIG.TOKEN_KEY);
      expect(localStorage.removeItem).toHaveBeenCalledWith(AUTH_CONFIG.REFRESH_TOKEN_KEY);
      expect(localStorage.removeItem).toHaveBeenCalledWith(AUTH_CONFIG.USER_KEY);
    });
  });

  describe('isAuthenticated', () => {
    it('should return true when user and token are present', () => {
      mockLocalStorage[AUTH_CONFIG.TOKEN_KEY] = 'test-token';
      mockLocalStorage[AUTH_CONFIG.USER_KEY] = JSON.stringify({
        id: '1',
        email: '<EMAIL>',
        name: 'Test User',
      });

      expect(authService.isAuthenticated()).toBe(true);
    });

    it('should return false when token is missing', () => {
      mockLocalStorage[AUTH_CONFIG.USER_KEY] = JSON.stringify({
        id: '1',
        email: '<EMAIL>',
        name: 'Test User',
      });

      expect(authService.isAuthenticated()).toBe(false);
    });

    it('should return false when user is missing', () => {
      mockLocalStorage[AUTH_CONFIG.TOKEN_KEY] = 'test-token';

      expect(authService.isAuthenticated()).toBe(false);
    });

    it('should return false when both are missing', () => {
      expect(authService.isAuthenticated()).toBe(false);
    });
  });

  describe('getCurrentUser', () => {
    it('should return user when stored', () => {
      const user = {
        id: '1',
        email: '<EMAIL>',
        name: 'Test User',
      };
      mockLocalStorage[AUTH_CONFIG.USER_KEY] = JSON.stringify(user);

      const result = authService.getCurrentUser();
      expect(result).toEqual(user);
    });

    it('should return null when no user stored', () => {
      const result = authService.getCurrentUser();
      expect(result).toBeNull();
    });

    it('should return null when stored user data is invalid JSON', () => {
      mockLocalStorage[AUTH_CONFIG.USER_KEY] = 'invalid-json';

      const result = authService.getCurrentUser();
      expect(result).toBeNull();
    });
  });

  describe('refreshToken', () => {
    it('should throw error when no refresh token available', async () => {
      await expect(authService.refreshToken()).rejects.toThrow(
        'No refresh token available'
      );
    });

    // Note: The actual refresh token implementation would require mocking the API service
    // This is a placeholder for when real API integration is implemented
  });
});
