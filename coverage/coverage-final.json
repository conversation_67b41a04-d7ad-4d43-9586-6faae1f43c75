{"/Users/<USER>/Documents/GitHub/COMCROW_CSA/src/constants/app-constants.ts": {"path": "/Users/<USER>/Documents/GitHub/COMCROW_CSA/src/constants/app-constants.ts", "statementMap": {"0": {"start": {"line": 2, "column": 13}, "end": {"line": 7, "column": 11}}, "1": {"start": {"line": 10, "column": 13}, "end": {"line": 14, "column": 11}}, "2": {"start": {"line": 17, "column": 13}, "end": {"line": 22, "column": 11}}, "3": {"start": {"line": 25, "column": 13}, "end": {"line": 31, "column": 11}}, "4": {"start": {"line": 34, "column": 13}, "end": {"line": 38, "column": 11}}, "5": {"start": {"line": 41, "column": 13}, "end": {"line": 47, "column": 11}}, "6": {"start": {"line": 50, "column": 13}, "end": {"line": 53, "column": 11}}, "7": {"start": {"line": 56, "column": 13}, "end": {"line": 62, "column": 11}}, "8": {"start": {"line": 65, "column": 13}, "end": {"line": 72, "column": 11}}, "9": {"start": {"line": 75, "column": 13}, "end": {"line": 79, "column": 11}}}, "fnMap": {}, "branchMap": {"0": {"loc": {"start": {"line": 11, "column": 12}, "end": {"line": 11, "column": 70}}, "type": "binary-expr", "locations": [{"start": {"line": 11, "column": 12}, "end": {"line": 11, "column": 41}}, {"start": {"line": 11, "column": 45}, "end": {"line": 11, "column": 70}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "f": {}, "b": {"0": [0, 0]}}, "/Users/<USER>/Documents/GitHub/COMCROW_CSA/src/hooks/use-auth.ts": {"path": "/Users/<USER>/Documents/GitHub/COMCROW_CSA/src/hooks/use-auth.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 57}}, "1": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 55}}, "2": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 60}}, "3": {"start": {"line": 6, "column": 23}, "end": {"line": 60, "column": 1}}, "4": {"start": {"line": 7, "column": 26}, "end": {"line": 7, "column": 53}}, "5": {"start": {"line": 8, "column": 36}, "end": {"line": 8, "column": 51}}, "6": {"start": {"line": 9, "column": 28}, "end": {"line": 9, "column": 57}}, "7": {"start": {"line": 12, "column": 2}, "end": {"line": 17, "column": 9}}, "8": {"start": {"line": 13, "column": 24}, "end": {"line": 13, "column": 52}}, "9": {"start": {"line": 14, "column": 4}, "end": {"line": 16, "column": 5}}, "10": {"start": {"line": 15, "column": 6}, "end": {"line": 15, "column": 27}}, "11": {"start": {"line": 19, "column": 16}, "end": {"line": 33, "column": 8}}, "12": {"start": {"line": 19, "column": 70}, "end": {"line": 33, "column": 5}}, "13": {"start": {"line": 20, "column": 4}, "end": {"line": 20, "column": 23}}, "14": {"start": {"line": 21, "column": 4}, "end": {"line": 21, "column": 19}}, "15": {"start": {"line": 23, "column": 4}, "end": {"line": 32, "column": 5}}, "16": {"start": {"line": 24, "column": 23}, "end": {"line": 24, "column": 67}}, "17": {"start": {"line": 25, "column": 6}, "end": {"line": 25, "column": 29}}, "18": {"start": {"line": 27, "column": 27}, "end": {"line": 27, "column": 100}}, "19": {"start": {"line": 28, "column": 6}, "end": {"line": 28, "column": 29}}, "20": {"start": {"line": 29, "column": 6}, "end": {"line": 29, "column": 16}}, "21": {"start": {"line": 31, "column": 6}, "end": {"line": 31, "column": 26}}, "22": {"start": {"line": 35, "column": 17}, "end": {"line": 48, "column": 8}}, "23": {"start": {"line": 35, "column": 40}, "end": {"line": 48, "column": 5}}, "24": {"start": {"line": 36, "column": 4}, "end": {"line": 36, "column": 23}}, "25": {"start": {"line": 37, "column": 4}, "end": {"line": 47, "column": 5}}, "26": {"start": {"line": 38, "column": 6}, "end": {"line": 38, "column": 33}}, "27": {"start": {"line": 39, "column": 6}, "end": {"line": 39, "column": 20}}, "28": {"start": {"line": 40, "column": 6}, "end": {"line": 40, "column": 21}}, "29": {"start": {"line": 43, "column": 6}, "end": {"line": 43, "column": 20}}, "30": {"start": {"line": 44, "column": 6}, "end": {"line": 44, "column": 21}}, "31": {"start": {"line": 46, "column": 6}, "end": {"line": 46, "column": 26}}, "32": {"start": {"line": 50, "column": 26}, "end": {"line": 50, "column": 72}}, "33": {"start": {"line": 52, "column": 2}, "end": {"line": 59, "column": 4}}, "34": {"start": {"line": 6, "column": 13}, "end": {"line": 6, "column": 23}}}, "fnMap": {"0": {"name": "(anonymous_7)", "decl": {"start": {"line": 6, "column": 23}, "end": {"line": 6, "column": 26}}, "loc": {"start": {"line": 6, "column": 28}, "end": {"line": 60, "column": 1}}}, "1": {"name": "(anonymous_8)", "decl": {"start": {"line": 12, "column": 12}, "end": {"line": 12, "column": 15}}, "loc": {"start": {"line": 12, "column": 17}, "end": {"line": 17, "column": 3}}}, "2": {"name": "(anonymous_9)", "decl": {"start": {"line": 19, "column": 28}, "end": {"line": 19, "column": 35}}, "loc": {"start": {"line": 19, "column": 70}, "end": {"line": 33, "column": 5}}}, "3": {"name": "(anonymous_10)", "decl": {"start": {"line": 19, "column": 70}, "end": {"line": 19, "column": null}}, "loc": {"start": {"line": 19, "column": 70}, "end": {"line": 33, "column": 3}}}, "4": {"name": "(anonymous_11)", "decl": {"start": {"line": 35, "column": 29}, "end": {"line": 35, "column": 38}}, "loc": {"start": {"line": 35, "column": 40}, "end": {"line": 48, "column": 5}}}, "5": {"name": "(anonymous_12)", "decl": {"start": {"line": 35, "column": 40}, "end": {"line": 35, "column": null}}, "loc": {"start": {"line": 35, "column": 40}, "end": {"line": 48, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 14, "column": 4}, "end": {"line": 16, "column": 5}}, "type": "if", "locations": [{"start": {"line": 14, "column": 4}, "end": {"line": 16, "column": 5}}, {"start": {}, "end": {}}]}, "1": {"loc": {"start": {"line": 14, "column": 8}, "end": {"line": 14, "column": 52}}, "type": "binary-expr", "locations": [{"start": {"line": 14, "column": 8}, "end": {"line": 14, "column": 19}}, {"start": {"line": 14, "column": 23}, "end": {"line": 14, "column": 52}}]}, "2": {"loc": {"start": {"line": 27, "column": 27}, "end": {"line": 27, "column": 100}}, "type": "cond-expr", "locations": [{"start": {"line": 27, "column": 50}, "end": {"line": 27, "column": 61}}, {"start": {"line": 27, "column": 64}, "end": {"line": 27, "column": 100}}]}, "3": {"loc": {"start": {"line": 50, "column": 26}, "end": {"line": 50, "column": 72}}, "type": "binary-expr", "locations": [{"start": {"line": 50, "column": 26}, "end": {"line": 50, "column": 39}}, {"start": {"line": 50, "column": 43}, "end": {"line": 50, "column": 72}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0]}}, "/Users/<USER>/Documents/GitHub/COMCROW_CSA/src/hooks/use-search.ts": {"path": "/Users/<USER>/Documents/GitHub/COMCROW_CSA/src/hooks/use-search.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 46}}, "1": {"start": {"line": 7, "column": 33}, "end": {"line": 58, "column": 2}}, "2": {"start": {"line": 60, "column": 25}, "end": {"line": 181, "column": 1}}, "3": {"start": {"line": 61, "column": 32}, "end": {"line": 66, "column": 4}}, "4": {"start": {"line": 68, "column": 17}, "end": {"line": 130, "column": 8}}, "5": {"start": {"line": 68, "column": 64}, "end": {"line": 130, "column": 5}}, "6": {"start": {"line": 69, "column": 4}, "end": {"line": 69, "column": 90}}, "7": {"start": {"line": 69, "column": 23}, "end": {"line": 69, "column": 88}}, "8": {"start": {"line": 71, "column": 4}, "end": {"line": 129, "column": 5}}, "9": {"start": {"line": 73, "column": 20}, "end": {"line": 79, "column": 8}}, "10": {"start": {"line": 81, "column": 6}, "end": {"line": 88, "column": 7}}, "11": {"start": {"line": 82, "column": 8}, "end": {"line": 82, "column": 70}}, "12": {"start": {"line": 82, "column": 37}, "end": {"line": 82, "column": 68}}, "13": {"start": {"line": 83, "column": 8}, "end": {"line": 87, "column": 12}}, "14": {"start": {"line": 83, "column": 27}, "end": {"line": 86, "column": null}}, "15": {"start": {"line": 91, "column": 30}, "end": {"line": 91, "column": 43}}, "16": {"start": {"line": 93, "column": 6}, "end": {"line": 95, "column": 7}}, "17": {"start": {"line": 94, "column": 8}, "end": {"line": 94, "column": 76}}, "18": {"start": {"line": 94, "column": 58}, "end": {"line": 94, "column": 74}}, "19": {"start": {"line": 97, "column": 6}, "end": {"line": 99, "column": 7}}, "20": {"start": {"line": 98, "column": 8}, "end": {"line": 98, "column": 84}}, "21": {"start": {"line": 98, "column": 58}, "end": {"line": 98, "column": 82}}, "22": {"start": {"line": 101, "column": 6}, "end": {"line": 103, "column": 7}}, "23": {"start": {"line": 102, "column": 8}, "end": {"line": 102, "column": 83}}, "24": {"start": {"line": 102, "column": 58}, "end": {"line": 102, "column": 81}}, "25": {"start": {"line": 105, "column": 6}, "end": {"line": 107, "column": 7}}, "26": {"start": {"line": 106, "column": 8}, "end": {"line": 106, "column": 81}}, "27": {"start": {"line": 106, "column": 58}, "end": {"line": 106, "column": 79}}, "28": {"start": {"line": 109, "column": 6}, "end": {"line": 111, "column": 7}}, "29": {"start": {"line": 110, "column": 8}, "end": {"line": 110, "column": 103}}, "30": {"start": {"line": 110, "column": 58}, "end": {"line": 110, "column": 101}}, "31": {"start": {"line": 114, "column": 26}, "end": {"line": 114, "column": 83}}, "32": {"start": {"line": 116, "column": 6}, "end": {"line": 121, "column": 9}}, "33": {"start": {"line": 124, "column": 6}, "end": {"line": 128, "column": 10}}, "34": {"start": {"line": 124, "column": 25}, "end": {"line": 127, "column": null}}, "35": {"start": {"line": 132, "column": 23}, "end": {"line": 134, "column": 8}}, "36": {"start": {"line": 133, "column": 4}, "end": {"line": 133, "column": 67}}, "37": {"start": {"line": 133, "column": 23}, "end": {"line": 133, "column": 65}}, "38": {"start": {"line": 136, "column": 22}, "end": {"line": 163, "column": 25}}, "39": {"start": {"line": 138, "column": 23}, "end": {"line": 138, "column": 128}}, "40": {"start": {"line": 139, "column": 20}, "end": {"line": 147, "column": 6}}, "41": {"start": {"line": 139, "column": 53}, "end": {"line": 147, "column": 6}}, "42": {"start": {"line": 149, "column": 23}, "end": {"line": 151, "column": 17}}, "43": {"start": {"line": 150, "column": 18}, "end": {"line": 150, "column": 56}}, "44": {"start": {"line": 150, "column": 34}, "end": {"line": 150, "column": 45}}, "45": {"start": {"line": 154, "column": 17}, "end": {"line": 154, "column": 76}}, "46": {"start": {"line": 155, "column": 17}, "end": {"line": 155, "column": 44}}, "47": {"start": {"line": 156, "column": 16}, "end": {"line": 156, "column": 41}}, "48": {"start": {"line": 157, "column": 4}, "end": {"line": 157, "column": 35}}, "49": {"start": {"line": 158, "column": 4}, "end": {"line": 158, "column": 105}}, "50": {"start": {"line": 159, "column": 4}, "end": {"line": 159, "column": 37}}, "51": {"start": {"line": 160, "column": 4}, "end": {"line": 160, "column": 36}}, "52": {"start": {"line": 161, "column": 4}, "end": {"line": 161, "column": 17}}, "53": {"start": {"line": 162, "column": 4}, "end": {"line": 162, "column": 36}}, "54": {"start": {"line": 165, "column": 22}, "end": {"line": 172, "column": 8}}, "55": {"start": {"line": 166, "column": 4}, "end": {"line": 171, "column": 7}}, "56": {"start": {"line": 174, "column": 2}, "end": {"line": 180, "column": 4}}, "57": {"start": {"line": 60, "column": 13}, "end": {"line": 60, "column": 25}}}, "fnMap": {"0": {"name": "(anonymous_7)", "decl": {"start": {"line": 60, "column": 25}, "end": {"line": 60, "column": 28}}, "loc": {"start": {"line": 60, "column": 30}, "end": {"line": 181, "column": 1}}}, "1": {"name": "(anonymous_8)", "decl": {"start": {"line": 68, "column": 29}, "end": {"line": 68, "column": 36}}, "loc": {"start": {"line": 68, "column": 64}, "end": {"line": 130, "column": 5}}}, "2": {"name": "(anonymous_9)", "decl": {"start": {"line": 68, "column": 64}, "end": {"line": 68, "column": null}}, "loc": {"start": {"line": 68, "column": 64}, "end": {"line": 130, "column": 3}}}, "3": {"name": "(anonymous_10)", "decl": {"start": {"line": 69, "column": 15}, "end": {"line": 69, "column": 19}}, "loc": {"start": {"line": 69, "column": 23}, "end": {"line": 69, "column": 88}}}, "4": {"name": "(anonymous_11)", "decl": {"start": {"line": 82, "column": 26}, "end": {"line": 82, "column": 33}}, "loc": {"start": {"line": 82, "column": 37}, "end": {"line": 82, "column": 68}}}, "5": {"name": "(anonymous_12)", "decl": {"start": {"line": 83, "column": 19}, "end": {"line": 83, "column": 23}}, "loc": {"start": {"line": 83, "column": 27}, "end": {"line": 86, "column": null}}}, "6": {"name": "(anonymous_13)", "decl": {"start": {"line": 94, "column": 53}, "end": {"line": 94, "column": 54}}, "loc": {"start": {"line": 94, "column": 58}, "end": {"line": 94, "column": 74}}}, "7": {"name": "(anonymous_14)", "decl": {"start": {"line": 98, "column": 53}, "end": {"line": 98, "column": 54}}, "loc": {"start": {"line": 98, "column": 58}, "end": {"line": 98, "column": 82}}}, "8": {"name": "(anonymous_15)", "decl": {"start": {"line": 102, "column": 53}, "end": {"line": 102, "column": 54}}, "loc": {"start": {"line": 102, "column": 58}, "end": {"line": 102, "column": 81}}}, "9": {"name": "(anonymous_16)", "decl": {"start": {"line": 106, "column": 53}, "end": {"line": 106, "column": 54}}, "loc": {"start": {"line": 106, "column": 58}, "end": {"line": 106, "column": 79}}}, "10": {"name": "(anonymous_17)", "decl": {"start": {"line": 110, "column": 53}, "end": {"line": 110, "column": 54}}, "loc": {"start": {"line": 110, "column": 58}, "end": {"line": 110, "column": 101}}}, "11": {"name": "(anonymous_18)", "decl": {"start": {"line": 124, "column": 17}, "end": {"line": 124, "column": 21}}, "loc": {"start": {"line": 124, "column": 25}, "end": {"line": 127, "column": null}}}, "12": {"name": "(anonymous_19)", "decl": {"start": {"line": 132, "column": 35}, "end": {"line": 132, "column": 38}}, "loc": {"start": {"line": 132, "column": 40}, "end": {"line": 134, "column": 3}}}, "13": {"name": "(anonymous_20)", "decl": {"start": {"line": 133, "column": 15}, "end": {"line": 133, "column": 19}}, "loc": {"start": {"line": 133, "column": 23}, "end": {"line": 133, "column": 65}}}, "14": {"name": "(anonymous_21)", "decl": {"start": {"line": 136, "column": 34}, "end": {"line": 136, "column": 37}}, "loc": {"start": {"line": 136, "column": 39}, "end": {"line": 163, "column": 3}}}, "15": {"name": "(anonymous_22)", "decl": {"start": {"line": 139, "column": 42}, "end": {"line": 139, "column": 49}}, "loc": {"start": {"line": 139, "column": 53}, "end": {"line": 147, "column": 6}}}, "16": {"name": "(anonymous_23)", "decl": {"start": {"line": 150, "column": 11}, "end": {"line": 150, "column": 14}}, "loc": {"start": {"line": 150, "column": 18}, "end": {"line": 150, "column": 56}}}, "17": {"name": "(anonymous_24)", "decl": {"start": {"line": 150, "column": 26}, "end": {"line": 150, "column": 30}}, "loc": {"start": {"line": 150, "column": 34}, "end": {"line": 150, "column": 45}}}, "18": {"name": "(anonymous_25)", "decl": {"start": {"line": 165, "column": 34}, "end": {"line": 165, "column": 37}}, "loc": {"start": {"line": 165, "column": 39}, "end": {"line": 172, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 93, "column": 6}, "end": {"line": 95, "column": 7}}, "type": "if", "locations": [{"start": {"line": 93, "column": 6}, "end": {"line": 95, "column": 7}}, {"start": {}, "end": {}}]}, "1": {"loc": {"start": {"line": 97, "column": 6}, "end": {"line": 99, "column": 7}}, "type": "if", "locations": [{"start": {"line": 97, "column": 6}, "end": {"line": 99, "column": 7}}, {"start": {}, "end": {}}]}, "2": {"loc": {"start": {"line": 101, "column": 6}, "end": {"line": 103, "column": 7}}, "type": "if", "locations": [{"start": {"line": 101, "column": 6}, "end": {"line": 103, "column": 7}}, {"start": {}, "end": {}}]}, "3": {"loc": {"start": {"line": 105, "column": 6}, "end": {"line": 107, "column": 7}}, "type": "if", "locations": [{"start": {"line": 105, "column": 6}, "end": {"line": 107, "column": 7}}, {"start": {}, "end": {}}]}, "4": {"loc": {"start": {"line": 109, "column": 6}, "end": {"line": 111, "column": 7}}, "type": "if", "locations": [{"start": {"line": 109, "column": 6}, "end": {"line": 111, "column": 7}}, {"start": {}, "end": {}}]}, "5": {"loc": {"start": {"line": 127, "column": 15}, "end": {"line": 127, "column": 71}}, "type": "cond-expr", "locations": [{"start": {"line": 127, "column": 40}, "end": {"line": 127, "column": 53}}, {"start": {"line": 127, "column": 56}, "end": {"line": 127, "column": 71}}]}, "6": {"loc": {"start": {"line": 146, "column": 6}, "end": {"line": 146, "column": 47}}, "type": "cond-expr", "locations": [{"start": {"line": 146, "column": 35}, "end": {"line": 146, "column": 40}}, {"start": {"line": 146, "column": 43}, "end": {"line": 146, "column": 47}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0]}}, "/Users/<USER>/Documents/GitHub/COMCROW_CSA/src/services/api-service.ts": {"path": "/Users/<USER>/Documents/GitHub/COMCROW_CSA/src/services/api-service.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 72}}, "1": {"start": {"line": 9, "column": 4}, "end": {"line": 9, "column": 39}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 38}}, "3": {"start": {"line": 15, "column": 4}, "end": {"line": 49, "column": null}}, "4": {"start": {"line": 18, "column": 16}, "end": {"line": 18, "column": 44}}, "5": {"start": {"line": 19, "column": 23}, "end": {"line": 19, "column": 44}}, "6": {"start": {"line": 20, "column": 22}, "end": {"line": 20, "column": 72}}, "7": {"start": {"line": 20, "column": 39}, "end": {"line": 20, "column": 57}}, "8": {"start": {"line": 22, "column": 4}, "end": {"line": 48, "column": 5}}, "9": {"start": {"line": 23, "column": 23}, "end": {"line": 28, "column": null}}, "10": {"start": {"line": 32, "column": 6}, "end": {"line": 32, "column": 30}}, "11": {"start": {"line": 34, "column": 6}, "end": {"line": 36, "column": 7}}, "12": {"start": {"line": 35, "column": 8}, "end": {"line": 35, "column": 66}}, "13": {"start": {"line": 38, "column": 6}, "end": {"line": 38, "column": 35}}, "14": {"start": {"line": 40, "column": 6}, "end": {"line": 40, "column": 30}}, "15": {"start": {"line": 41, "column": 6}, "end": {"line": 46, "column": 7}}, "16": {"start": {"line": 42, "column": 8}, "end": {"line": 44, "column": 9}}, "17": {"start": {"line": 43, "column": 10}, "end": {"line": 43, "column": 45}}, "18": {"start": {"line": 45, "column": 8}, "end": {"line": 45, "column": 20}}, "19": {"start": {"line": 47, "column": 6}, "end": {"line": 47, "column": 52}}, "20": {"start": {"line": 53, "column": 4}, "end": {"line": 56, "column": 7}}, "21": {"start": {"line": 65, "column": 4}, "end": {"line": 69, "column": 7}}, "22": {"start": {"line": 78, "column": 4}, "end": {"line": 82, "column": 7}}, "23": {"start": {"line": 90, "column": 4}, "end": {"line": 93, "column": 7}}, "24": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 13}}, "25": {"start": {"line": 104, "column": 13}, "end": {"line": 104, "column": 43}}}, "fnMap": {"0": {"name": "(anonymous_7)", "decl": {"start": {"line": 8, "column": 2}, "end": {"line": 8, "column": null}}, "loc": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 3}}}, "1": {"name": "(anonymous_8)", "decl": {"start": {"line": 14, "column": 16}, "end": {"line": 14, "column": 23}}, "loc": {"start": {"line": 14, "column": 23}, "end": {"line": 49, "column": null}}}, "2": {"name": "(anonymous_9)", "decl": {"start": {"line": 15, "column": 4}, "end": {"line": 15, "column": 20}}, "loc": {"start": {"line": 16, "column": 29}, "end": {"line": 49, "column": 3}}}, "3": {"name": "(anonymous_10)", "decl": {"start": {"line": 20, "column": 33}, "end": {"line": 20, "column": 36}}, "loc": {"start": {"line": 20, "column": 39}, "end": {"line": 20, "column": 57}}}, "4": {"name": "(anonymous_11)", "decl": {"start": {"line": 52, "column": 8}, "end": {"line": 52, "column": 11}}, "loc": {"start": {"line": 52, "column": 65}, "end": {"line": 57, "column": null}}}, "5": {"name": "(anonymous_13)", "decl": {"start": {"line": 60, "column": 8}, "end": {"line": 60, "column": 12}}, "loc": {"start": {"line": 63, "column": 36}, "end": {"line": 70, "column": null}}}, "6": {"name": "(anonymous_15)", "decl": {"start": {"line": 73, "column": 8}, "end": {"line": 73, "column": 11}}, "loc": {"start": {"line": 76, "column": 36}, "end": {"line": 83, "column": null}}}, "7": {"name": "(anonymous_17)", "decl": {"start": {"line": 86, "column": 8}, "end": {"line": 86, "column": 14}}, "loc": {"start": {"line": 88, "column": 36}, "end": {"line": 94, "column": null}}}, "8": {"name": "(anonymous_19)", "decl": {"start": {"line": 97, "column": 2}, "end": {"line": 97, "column": 14}}, "loc": {"start": {"line": 97, "column": 28}, "end": {"line": 100, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 16, "column": 4}, "end": {"line": 16, "column": 29}}, "type": "default-arg", "locations": [{"start": {"line": 16, "column": 27}, "end": {"line": 16, "column": 29}}]}, "1": {"loc": {"start": {"line": 34, "column": 6}, "end": {"line": 36, "column": 7}}, "type": "if", "locations": [{"start": {"line": 34, "column": 6}, "end": {"line": 36, "column": 7}}, {"start": {}, "end": {}}]}, "2": {"loc": {"start": {"line": 41, "column": 6}, "end": {"line": 46, "column": 7}}, "type": "if", "locations": [{"start": {"line": 41, "column": 6}, "end": {"line": 46, "column": 7}}, {"start": {}, "end": {}}]}, "3": {"loc": {"start": {"line": 42, "column": 8}, "end": {"line": 44, "column": 9}}, "type": "if", "locations": [{"start": {"line": 42, "column": 8}, "end": {"line": 44, "column": 9}}, {"start": {}, "end": {}}]}, "4": {"loc": {"start": {"line": 67, "column": 12}, "end": {"line": 67, "column": 51}}, "type": "cond-expr", "locations": [{"start": {"line": 67, "column": 19}, "end": {"line": 67, "column": 39}}, {"start": {"line": 67, "column": 42}, "end": {"line": 67, "column": 51}}]}, "5": {"loc": {"start": {"line": 80, "column": 12}, "end": {"line": 80, "column": 51}}, "type": "cond-expr", "locations": [{"start": {"line": 80, "column": 19}, "end": {"line": 80, "column": 39}}, {"start": {"line": 80, "column": 42}, "end": {"line": 80, "column": 51}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0}, "b": {"0": [0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0]}}, "/Users/<USER>/Documents/GitHub/COMCROW_CSA/src/services/auth-service.ts": {"path": "/Users/<USER>/Documents/GitHub/COMCROW_CSA/src/services/auth-service.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 43}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 73}}, "2": {"start": {"line": 19, "column": 4}, "end": {"line": 51, "column": 5}}, "3": {"start": {"line": 21, "column": 6}, "end": {"line": 21, "column": 62}}, "4": {"start": {"line": 21, "column": 35}, "end": {"line": 21, "column": 60}}, "5": {"start": {"line": 24, "column": 6}, "end": {"line": 42, "column": 7}}, "6": {"start": {"line": 25, "column": 43}, "end": {"line": 33, "column": 10}}, "7": {"start": {"line": 36, "column": 8}, "end": {"line": 36, "column": 72}}, "8": {"start": {"line": 37, "column": 8}, "end": {"line": 37, "column": 42}}, "9": {"start": {"line": 39, "column": 8}, "end": {"line": 39, "column": 28}}, "10": {"start": {"line": 41, "column": 8}, "end": {"line": 41, "column": 60}}, "11": {"start": {"line": 47, "column": 6}, "end": {"line": 49, "column": 7}}, "12": {"start": {"line": 48, "column": 8}, "end": {"line": 48, "column": 20}}, "13": {"start": {"line": 50, "column": 6}, "end": {"line": 50, "column": 60}}, "14": {"start": {"line": 56, "column": 4}, "end": {"line": 65, "column": 5}}, "15": {"start": {"line": 58, "column": 6}, "end": {"line": 58, "column": 29}}, "16": {"start": {"line": 64, "column": 6}, "end": {"line": 64, "column": 29}}, "17": {"start": {"line": 70, "column": 18}, "end": {"line": 70, "column": 39}}, "18": {"start": {"line": 71, "column": 17}, "end": {"line": 71, "column": 37}}, "19": {"start": {"line": 72, "column": 4}, "end": {"line": 72, "column": 29}}, "20": {"start": {"line": 77, "column": 4}, "end": {"line": 77, "column": 32}}, "21": {"start": {"line": 82, "column": 25}, "end": {"line": 82, "column": 53}}, "22": {"start": {"line": 83, "column": 4}, "end": {"line": 85, "column": 5}}, "23": {"start": {"line": 84, "column": 6}, "end": {"line": 84, "column": 52}}, "24": {"start": {"line": 87, "column": 4}, "end": {"line": 98, "column": 5}}, "25": {"start": {"line": 89, "column": 23}, "end": {"line": 91, "column": 8}}, "26": {"start": {"line": 93, "column": 6}, "end": {"line": 93, "column": 53}}, "27": {"start": {"line": 94, "column": 6}, "end": {"line": 94, "column": 28}}, "28": {"start": {"line": 96, "column": 6}, "end": {"line": 96, "column": 29}}, "29": {"start": {"line": 97, "column": 6}, "end": {"line": 97, "column": 54}}, "30": {"start": {"line": 103, "column": 4}, "end": {"line": 103, "column": 55}}, "31": {"start": {"line": 104, "column": 4}, "end": {"line": 104, "column": 70}}, "32": {"start": {"line": 108, "column": 4}, "end": {"line": 108, "column": 69}}, "33": {"start": {"line": 112, "column": 4}, "end": {"line": 112, "column": 55}}, "34": {"start": {"line": 116, "column": 4}, "end": {"line": 116, "column": 63}}, "35": {"start": {"line": 120, "column": 20}, "end": {"line": 120, "column": 62}}, "36": {"start": {"line": 121, "column": 4}, "end": {"line": 121, "column": 30}}, "37": {"start": {"line": 121, "column": 18}, "end": {"line": 121, "column": 30}}, "38": {"start": {"line": 123, "column": 4}, "end": {"line": 127, "column": 5}}, "39": {"start": {"line": 124, "column": 6}, "end": {"line": 124, "column": 33}}, "40": {"start": {"line": 126, "column": 6}, "end": {"line": 126, "column": 18}}, "41": {"start": {"line": 131, "column": 4}, "end": {"line": 131, "column": 51}}, "42": {"start": {"line": 132, "column": 4}, "end": {"line": 132, "column": 59}}, "43": {"start": {"line": 133, "column": 4}, "end": {"line": 133, "column": 50}}, "44": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 13}}, "45": {"start": {"line": 138, "column": 13}, "end": {"line": 138, "column": 45}}}, "fnMap": {"0": {"name": "(anonymous_7)", "decl": {"start": {"line": 18, "column": 8}, "end": {"line": 18, "column": 13}}, "loc": {"start": {"line": 18, "column": 43}, "end": {"line": 52, "column": null}}}, "1": {"name": "(anonymous_9)", "decl": {"start": {"line": 21, "column": 24}, "end": {"line": 21, "column": 31}}, "loc": {"start": {"line": 21, "column": 35}, "end": {"line": 21, "column": 60}}}, "2": {"name": "(anonymous_10)", "decl": {"start": {"line": 55, "column": 8}, "end": {"line": 55, "column": 14}}, "loc": {"start": {"line": 55, "column": 14}, "end": {"line": 66, "column": null}}}, "3": {"name": "(anonymous_12)", "decl": {"start": {"line": 69, "column": 2}, "end": {"line": 69, "column": 17}}, "loc": {"start": {"line": 69, "column": 17}, "end": {"line": 73, "column": 3}}}, "4": {"name": "(anonymous_13)", "decl": {"start": {"line": 76, "column": 2}, "end": {"line": 76, "column": 16}}, "loc": {"start": {"line": 76, "column": 16}, "end": {"line": 78, "column": 3}}}, "5": {"name": "(anonymous_14)", "decl": {"start": {"line": 81, "column": 8}, "end": {"line": 81, "column": 20}}, "loc": {"start": {"line": 81, "column": 20}, "end": {"line": 99, "column": null}}}, "6": {"name": "(anonymous_16)", "decl": {"start": {"line": 102, "column": 10}, "end": {"line": 102, "column": 21}}, "loc": {"start": {"line": 102, "column": 57}, "end": {"line": 105, "column": 3}}}, "7": {"name": "(anonymous_17)", "decl": {"start": {"line": 107, "column": 10}, "end": {"line": 107, "column": 19}}, "loc": {"start": {"line": 107, "column": 30}, "end": {"line": 109, "column": 3}}}, "8": {"name": "(anonymous_18)", "decl": {"start": {"line": 111, "column": 10}, "end": {"line": 111, "column": 24}}, "loc": {"start": {"line": 111, "column": 24}, "end": {"line": 113, "column": 3}}}, "9": {"name": "(anonymous_19)", "decl": {"start": {"line": 115, "column": 10}, "end": {"line": 115, "column": 31}}, "loc": {"start": {"line": 115, "column": 31}, "end": {"line": 117, "column": 3}}}, "10": {"name": "(anonymous_20)", "decl": {"start": {"line": 119, "column": 10}, "end": {"line": 119, "column": 23}}, "loc": {"start": {"line": 119, "column": 23}, "end": {"line": 128, "column": 3}}}, "11": {"name": "(anonymous_21)", "decl": {"start": {"line": 130, "column": 10}, "end": {"line": 130, "column": 25}}, "loc": {"start": {"line": 130, "column": 25}, "end": {"line": 134, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 24, "column": 6}, "end": {"line": 42, "column": 7}}, "type": "if", "locations": [{"start": {"line": 24, "column": 6}, "end": {"line": 42, "column": 7}}, {"start": {"line": 40, "column": 13}, "end": {"line": 42, "column": 7}}]}, "1": {"loc": {"start": {"line": 24, "column": 10}, "end": {"line": 24, "column": 92}}, "type": "binary-expr", "locations": [{"start": {"line": 24, "column": 10}, "end": {"line": 24, "column": 50}}, {"start": {"line": 24, "column": 54}, "end": {"line": 24, "column": 92}}]}, "2": {"loc": {"start": {"line": 47, "column": 6}, "end": {"line": 49, "column": 7}}, "type": "if", "locations": [{"start": {"line": 47, "column": 6}, "end": {"line": 49, "column": 7}}, {"start": {}, "end": {}}]}, "3": {"loc": {"start": {"line": 72, "column": 14}, "end": {"line": 72, "column": 27}}, "type": "binary-expr", "locations": [{"start": {"line": 72, "column": 14}, "end": {"line": 72, "column": 19}}, {"start": {"line": 72, "column": 23}, "end": {"line": 72, "column": 27}}]}, "4": {"loc": {"start": {"line": 83, "column": 4}, "end": {"line": 85, "column": 5}}, "type": "if", "locations": [{"start": {"line": 83, "column": 4}, "end": {"line": 85, "column": 5}}, {"start": {}, "end": {}}]}, "5": {"loc": {"start": {"line": 121, "column": 4}, "end": {"line": 121, "column": 30}}, "type": "if", "locations": [{"start": {"line": 121, "column": 4}, "end": {"line": 121, "column": 30}}, {"start": {}, "end": {}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0]}}, "/Users/<USER>/Documents/GitHub/COMCROW_CSA/src/services/search-service.ts": {"path": "/Users/<USER>/Documents/GitHub/COMCROW_CSA/src/services/search-service.ts", "statementMap": {"0": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 75}}, "1": {"start": {"line": 6, "column": 33}, "end": {"line": 57, "column": 2}}, "2": {"start": {"line": 60, "column": 10}, "end": {"line": 60, "column": 57}}, "3": {"start": {"line": 67, "column": 4}, "end": {"line": 119, "column": 5}}, "4": {"start": {"line": 69, "column": 6}, "end": {"line": 69, "column": 26}}, "5": {"start": {"line": 72, "column": 6}, "end": {"line": 72, "column": 51}}, "6": {"start": {"line": 75, "column": 6}, "end": {"line": 75, "column": 49}}, "7": {"start": {"line": 76, "column": 6}, "end": {"line": 76, "column": 28}}, "8": {"start": {"line": 78, "column": 6}, "end": {"line": 80, "column": 7}}, "9": {"start": {"line": 79, "column": 8}, "end": {"line": 79, "column": 44}}, "10": {"start": {"line": 82, "column": 6}, "end": {"line": 82, "column": 49}}, "11": {"start": {"line": 83, "column": 6}, "end": {"line": 83, "column": 29}}, "12": {"start": {"line": 85, "column": 6}, "end": {"line": 87, "column": 7}}, "13": {"start": {"line": 86, "column": 8}, "end": {"line": 86, "column": 44}}, "14": {"start": {"line": 89, "column": 6}, "end": {"line": 89, "column": 47}}, "15": {"start": {"line": 90, "column": 6}, "end": {"line": 90, "column": 28}}, "16": {"start": {"line": 92, "column": 6}, "end": {"line": 94, "column": 7}}, "17": {"start": {"line": 93, "column": 8}, "end": {"line": 93, "column": 44}}, "18": {"start": {"line": 96, "column": 6}, "end": {"line": 96, "column": 45}}, "19": {"start": {"line": 97, "column": 6}, "end": {"line": 97, "column": 28}}, "20": {"start": {"line": 99, "column": 6}, "end": {"line": 101, "column": 7}}, "21": {"start": {"line": 100, "column": 8}, "end": {"line": 100, "column": 44}}, "22": {"start": {"line": 104, "column": 32}, "end": {"line": 104, "column": 77}}, "23": {"start": {"line": 106, "column": 6}, "end": {"line": 106, "column": 44}}, "24": {"start": {"line": 111, "column": 6}, "end": {"line": 111, "column": 31}}, "25": {"start": {"line": 113, "column": 6}, "end": {"line": 115, "column": 7}}, "26": {"start": {"line": 114, "column": 8}, "end": {"line": 114, "column": 20}}, "27": {"start": {"line": 116, "column": 6}, "end": {"line": 116, "column": 52}}, "28": {"start": {"line": 118, "column": 6}, "end": {"line": 118, "column": 34}}, "29": {"start": {"line": 124, "column": 4}, "end": {"line": 127, "column": 5}}, "30": {"start": {"line": 125, "column": 6}, "end": {"line": 125, "column": 35}}, "31": {"start": {"line": 126, "column": 6}, "end": {"line": 126, "column": 34}}, "32": {"start": {"line": 132, "column": 4}, "end": {"line": 159, "column": 5}}, "33": {"start": {"line": 133, "column": 22}, "end": {"line": 141, "column": 8}}, "34": {"start": {"line": 143, "column": 25}, "end": {"line": 154, "column": 18}}, "35": {"start": {"line": 145, "column": 36}, "end": {"line": 153, "column": 19}}, "36": {"start": {"line": 156, "column": 6}, "end": {"line": 156, "column": 24}}, "37": {"start": {"line": 158, "column": 6}, "end": {"line": 158, "column": 47}}, "38": {"start": {"line": 164, "column": 4}, "end": {"line": 182, "column": 7}}, "39": {"start": {"line": 166, "column": 6}, "end": {"line": 166, "column": 63}}, "40": {"start": {"line": 166, "column": 50}, "end": {"line": 166, "column": 63}}, "41": {"start": {"line": 167, "column": 6}, "end": {"line": 167, "column": 64}}, "42": {"start": {"line": 167, "column": 51}, "end": {"line": 167, "column": 64}}, "43": {"start": {"line": 170, "column": 6}, "end": {"line": 170, "column": 75}}, "44": {"start": {"line": 170, "column": 62}, "end": {"line": 170, "column": 75}}, "45": {"start": {"line": 173, "column": 6}, "end": {"line": 173, "column": 73}}, "46": {"start": {"line": 173, "column": 60}, "end": {"line": 173, "column": 73}}, "47": {"start": {"line": 176, "column": 6}, "end": {"line": 176, "column": 76}}, "48": {"start": {"line": 176, "column": 63}, "end": {"line": 176, "column": 76}}, "49": {"start": {"line": 179, "column": 6}, "end": {"line": 179, "column": 73}}, "50": {"start": {"line": 179, "column": 60}, "end": {"line": 179, "column": 73}}, "51": {"start": {"line": 181, "column": 6}, "end": {"line": 181, "column": 18}}, "52": {"start": {"line": 186, "column": 4}, "end": {"line": 186, "column": 59}}, "53": {"start": {"line": 186, "column": 34}, "end": {"line": 186, "column": 57}}, "54": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 13}}, "55": {"start": {"line": 191, "column": 13}, "end": {"line": 191, "column": 49}}}, "fnMap": {"0": {"name": "(anonymous_7)", "decl": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 13}}, "loc": {"start": {"line": 59, "column": 0}, "end": {"line": 188, "column": 1}}}, "1": {"name": "(anonymous_8)", "decl": {"start": {"line": 63, "column": 8}, "end": {"line": 63, "column": 14}}, "loc": {"start": {"line": 65, "column": 59}, "end": {"line": 120, "column": null}}}, "2": {"name": "(anonymous_10)", "decl": {"start": {"line": 123, "column": 2}, "end": {"line": 123, "column": 14}}, "loc": {"start": {"line": 123, "column": 14}, "end": {"line": 128, "column": 3}}}, "3": {"name": "(anonymous_11)", "decl": {"start": {"line": 131, "column": 8}, "end": {"line": 131, "column": 19}}, "loc": {"start": {"line": 131, "column": 40}, "end": {"line": 160, "column": null}}}, "4": {"name": "(anonymous_13)", "decl": {"start": {"line": 145, "column": 25}, "end": {"line": 145, "column": 32}}, "loc": {"start": {"line": 145, "column": 36}, "end": {"line": 153, "column": 19}}}, "5": {"name": "(anonymous_14)", "decl": {"start": {"line": 163, "column": 10}, "end": {"line": 163, "column": 25}}, "loc": {"start": {"line": 163, "column": 72}, "end": {"line": 183, "column": 3}}}, "6": {"name": "(anonymous_15)", "decl": {"start": {"line": 164, "column": 28}, "end": {"line": 164, "column": 35}}, "loc": {"start": {"line": 164, "column": 38}, "end": {"line": 182, "column": 5}}}, "7": {"name": "(anonymous_16)", "decl": {"start": {"line": 185, "column": 10}, "end": {"line": 185, "column": 15}}, "loc": {"start": {"line": 185, "column": 26}, "end": {"line": 187, "column": 3}}}, "8": {"name": "(anonymous_17)", "decl": {"start": {"line": 186, "column": 23}, "end": {"line": 186, "column": 30}}, "loc": {"start": {"line": 186, "column": 34}, "end": {"line": 186, "column": 57}}}}, "branchMap": {"0": {"loc": {"start": {"line": 75, "column": 6}, "end": {"line": 75, "column": 48}}, "type": "cond-expr", "locations": [{"start": {"line": 75, "column": 16}, "end": {"line": 75, "column": 19}}, {"start": {"line": 75, "column": 6}, "end": {"line": 75, "column": 48}}]}, "1": {"loc": {"start": {"line": 75, "column": 6}, "end": {"line": 75, "column": 19}}, "type": "binary-expr", "locations": [{"start": {"line": 75, "column": 6}, "end": {"line": 75, "column": 19}}, {"start": {"line": 75, "column": 6}, "end": {"line": 75, "column": 19}}]}, "2": {"loc": {"start": {"line": 78, "column": 6}, "end": {"line": 80, "column": 7}}, "type": "if", "locations": [{"start": {"line": 78, "column": 6}, "end": {"line": 80, "column": 7}}, {"start": {}, "end": {}}]}, "3": {"loc": {"start": {"line": 82, "column": 6}, "end": {"line": 82, "column": 48}}, "type": "cond-expr", "locations": [{"start": {"line": 82, "column": 16}, "end": {"line": 82, "column": 19}}, {"start": {"line": 82, "column": 6}, "end": {"line": 82, "column": 48}}]}, "4": {"loc": {"start": {"line": 82, "column": 6}, "end": {"line": 82, "column": 19}}, "type": "binary-expr", "locations": [{"start": {"line": 82, "column": 6}, "end": {"line": 82, "column": 19}}, {"start": {"line": 82, "column": 6}, "end": {"line": 82, "column": 19}}]}, "5": {"loc": {"start": {"line": 85, "column": 6}, "end": {"line": 87, "column": 7}}, "type": "if", "locations": [{"start": {"line": 85, "column": 6}, "end": {"line": 87, "column": 7}}, {"start": {}, "end": {}}]}, "6": {"loc": {"start": {"line": 89, "column": 6}, "end": {"line": 89, "column": 46}}, "type": "cond-expr", "locations": [{"start": {"line": 89, "column": 16}, "end": {"line": 89, "column": 19}}, {"start": {"line": 89, "column": 6}, "end": {"line": 89, "column": 46}}]}, "7": {"loc": {"start": {"line": 89, "column": 6}, "end": {"line": 89, "column": 19}}, "type": "binary-expr", "locations": [{"start": {"line": 89, "column": 6}, "end": {"line": 89, "column": 19}}, {"start": {"line": 89, "column": 6}, "end": {"line": 89, "column": 19}}]}, "8": {"loc": {"start": {"line": 92, "column": 6}, "end": {"line": 94, "column": 7}}, "type": "if", "locations": [{"start": {"line": 92, "column": 6}, "end": {"line": 94, "column": 7}}, {"start": {}, "end": {}}]}, "9": {"loc": {"start": {"line": 96, "column": 6}, "end": {"line": 96, "column": 44}}, "type": "cond-expr", "locations": [{"start": {"line": 96, "column": 16}, "end": {"line": 96, "column": 19}}, {"start": {"line": 96, "column": 6}, "end": {"line": 96, "column": 44}}]}, "10": {"loc": {"start": {"line": 96, "column": 6}, "end": {"line": 96, "column": 19}}, "type": "binary-expr", "locations": [{"start": {"line": 96, "column": 6}, "end": {"line": 96, "column": 19}}, {"start": {"line": 96, "column": 6}, "end": {"line": 96, "column": 19}}]}, "11": {"loc": {"start": {"line": 99, "column": 6}, "end": {"line": 101, "column": 7}}, "type": "if", "locations": [{"start": {"line": 99, "column": 6}, "end": {"line": 101, "column": 7}}, {"start": {}, "end": {}}]}, "12": {"loc": {"start": {"line": 106, "column": 6}, "end": {"line": 106, "column": 43}}, "type": "cond-expr", "locations": [{"start": {"line": 106, "column": 16}, "end": {"line": 106, "column": 19}}, {"start": {"line": 106, "column": 6}, "end": {"line": 106, "column": 43}}]}, "13": {"loc": {"start": {"line": 106, "column": 6}, "end": {"line": 106, "column": 19}}, "type": "binary-expr", "locations": [{"start": {"line": 106, "column": 6}, "end": {"line": 106, "column": 19}}, {"start": {"line": 106, "column": 6}, "end": {"line": 106, "column": 19}}]}, "14": {"loc": {"start": {"line": 113, "column": 6}, "end": {"line": 115, "column": 7}}, "type": "if", "locations": [{"start": {"line": 113, "column": 6}, "end": {"line": 115, "column": 7}}, {"start": {}, "end": {}}]}, "15": {"loc": {"start": {"line": 113, "column": 10}, "end": {"line": 113, "column": 72}}, "type": "binary-expr", "locations": [{"start": {"line": 113, "column": 10}, "end": {"line": 113, "column": 32}}, {"start": {"line": 113, "column": 36}, "end": {"line": 113, "column": 72}}]}, "16": {"loc": {"start": {"line": 124, "column": 4}, "end": {"line": 127, "column": 5}}, "type": "if", "locations": [{"start": {"line": 124, "column": 4}, "end": {"line": 127, "column": 5}}, {"start": {}, "end": {}}]}, "17": {"loc": {"start": {"line": 152, "column": 10}, "end": {"line": 152, "column": 51}}, "type": "cond-expr", "locations": [{"start": {"line": 152, "column": 39}, "end": {"line": 152, "column": 44}}, {"start": {"line": 152, "column": 47}, "end": {"line": 152, "column": 51}}]}, "18": {"loc": {"start": {"line": 166, "column": 6}, "end": {"line": 166, "column": 63}}, "type": "if", "locations": [{"start": {"line": 166, "column": 6}, "end": {"line": 166, "column": 63}}, {"start": {}, "end": {}}]}, "19": {"loc": {"start": {"line": 166, "column": 10}, "end": {"line": 166, "column": 48}}, "type": "binary-expr", "locations": [{"start": {"line": 166, "column": 10}, "end": {"line": 166, "column": 22}}, {"start": {"line": 166, "column": 26}, "end": {"line": 166, "column": 48}}]}, "20": {"loc": {"start": {"line": 167, "column": 6}, "end": {"line": 167, "column": 64}}, "type": "if", "locations": [{"start": {"line": 167, "column": 6}, "end": {"line": 167, "column": 64}}, {"start": {}, "end": {}}]}, "21": {"loc": {"start": {"line": 167, "column": 10}, "end": {"line": 167, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 167, "column": 10}, "end": {"line": 167, "column": 23}}, {"start": {"line": 167, "column": 27}, "end": {"line": 167, "column": 49}}]}, "22": {"loc": {"start": {"line": 170, "column": 6}, "end": {"line": 170, "column": 75}}, "type": "if", "locations": [{"start": {"line": 170, "column": 6}, "end": {"line": 170, "column": 75}}, {"start": {}, "end": {}}]}, "23": {"loc": {"start": {"line": 170, "column": 10}, "end": {"line": 170, "column": 60}}, "type": "binary-expr", "locations": [{"start": {"line": 170, "column": 10}, "end": {"line": 170, "column": 26}}, {"start": {"line": 170, "column": 30}, "end": {"line": 170, "column": 60}}]}, "24": {"loc": {"start": {"line": 173, "column": 6}, "end": {"line": 173, "column": 73}}, "type": "if", "locations": [{"start": {"line": 173, "column": 6}, "end": {"line": 173, "column": 73}}, {"start": {}, "end": {}}]}, "25": {"loc": {"start": {"line": 173, "column": 10}, "end": {"line": 173, "column": 58}}, "type": "binary-expr", "locations": [{"start": {"line": 173, "column": 10}, "end": {"line": 173, "column": 25}}, {"start": {"line": 173, "column": 29}, "end": {"line": 173, "column": 58}}]}, "26": {"loc": {"start": {"line": 176, "column": 6}, "end": {"line": 176, "column": 76}}, "type": "if", "locations": [{"start": {"line": 176, "column": 6}, "end": {"line": 176, "column": 76}}, {"start": {}, "end": {}}]}, "27": {"loc": {"start": {"line": 176, "column": 10}, "end": {"line": 176, "column": 61}}, "type": "binary-expr", "locations": [{"start": {"line": 176, "column": 10}, "end": {"line": 176, "column": 31}}, {"start": {"line": 176, "column": 35}, "end": {"line": 176, "column": 61}}]}, "28": {"loc": {"start": {"line": 179, "column": 6}, "end": {"line": 179, "column": 73}}, "type": "if", "locations": [{"start": {"line": 179, "column": 6}, "end": {"line": 179, "column": 73}}, {"start": {}, "end": {}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [0, 0], "23": [0, 0], "24": [0, 0], "25": [0, 0], "26": [0, 0], "27": [0, 0], "28": [0, 0]}}, "/Users/<USER>/Documents/GitHub/COMCROW_CSA/src/utils/storage-utils.ts": {"path": "/Users/<USER>/Documents/GitHub/COMCROW_CSA/src/utils/storage-utils.ts", "statementMap": {"0": {"start": {"line": 5, "column": 4}, "end": {"line": 12, "column": 5}}, "1": {"start": {"line": 6, "column": 30}, "end": {"line": 6, "column": 51}}, "2": {"start": {"line": 7, "column": 6}, "end": {"line": 7, "column": 49}}, "3": {"start": {"line": 8, "column": 6}, "end": {"line": 8, "column": 18}}, "4": {"start": {"line": 10, "column": 6}, "end": {"line": 10, "column": 60}}, "5": {"start": {"line": 11, "column": 6}, "end": {"line": 11, "column": 19}}, "6": {"start": {"line": 17, "column": 4}, "end": {"line": 26, "column": 5}}, "7": {"start": {"line": 18, "column": 19}, "end": {"line": 18, "column": 44}}, "8": {"start": {"line": 19, "column": 6}, "end": {"line": 21, "column": 7}}, "9": {"start": {"line": 20, "column": 8}, "end": {"line": 20, "column": 36}}, "10": {"start": {"line": 22, "column": 6}, "end": {"line": 22, "column": 30}}, "11": {"start": {"line": 24, "column": 6}, "end": {"line": 24, "column": 63}}, "12": {"start": {"line": 25, "column": 6}, "end": {"line": 25, "column": 34}}, "13": {"start": {"line": 31, "column": 4}, "end": {"line": 37, "column": 5}}, "14": {"start": {"line": 32, "column": 6}, "end": {"line": 32, "column": 35}}, "15": {"start": {"line": 33, "column": 6}, "end": {"line": 33, "column": 18}}, "16": {"start": {"line": 35, "column": 6}, "end": {"line": 35, "column": 64}}, "17": {"start": {"line": 36, "column": 6}, "end": {"line": 36, "column": 19}}, "18": {"start": {"line": 42, "column": 4}, "end": {"line": 48, "column": 5}}, "19": {"start": {"line": 43, "column": 6}, "end": {"line": 43, "column": 27}}, "20": {"start": {"line": 44, "column": 6}, "end": {"line": 44, "column": 18}}, "21": {"start": {"line": 46, "column": 6}, "end": {"line": 46, "column": 59}}, "22": {"start": {"line": 47, "column": 6}, "end": {"line": 47, "column": 19}}, "23": {"start": {"line": 53, "column": 4}, "end": {"line": 60, "column": 5}}, "24": {"start": {"line": 54, "column": 19}, "end": {"line": 54, "column": 42}}, "25": {"start": {"line": 55, "column": 6}, "end": {"line": 55, "column": 41}}, "26": {"start": {"line": 56, "column": 6}, "end": {"line": 56, "column": 36}}, "27": {"start": {"line": 57, "column": 6}, "end": {"line": 57, "column": 18}}, "28": {"start": {"line": 59, "column": 6}, "end": {"line": 59, "column": 19}}, "29": {"start": {"line": 65, "column": 15}, "end": {"line": 65, "column": 16}}, "30": {"start": {"line": 67, "column": 4}, "end": {"line": 75, "column": 5}}, "31": {"start": {"line": 68, "column": 6}, "end": {"line": 72, "column": 7}}, "32": {"start": {"line": 69, "column": 8}, "end": {"line": 71, "column": 9}}, "33": {"start": {"line": 70, "column": 10}, "end": {"line": 70, "column": 56}}, "34": {"start": {"line": 74, "column": 6}, "end": {"line": 74, "column": 63}}, "35": {"start": {"line": 78, "column": 22}, "end": {"line": 78, "column": 37}}, "36": {"start": {"line": 80, "column": 4}, "end": {"line": 80, "column": 31}}, "37": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 13}}, "38": {"start": {"line": 88, "column": 4}, "end": {"line": 95, "column": 5}}, "39": {"start": {"line": 89, "column": 30}, "end": {"line": 89, "column": 51}}, "40": {"start": {"line": 90, "column": 6}, "end": {"line": 90, "column": 51}}, "41": {"start": {"line": 91, "column": 6}, "end": {"line": 91, "column": 18}}, "42": {"start": {"line": 93, "column": 6}, "end": {"line": 93, "column": 62}}, "43": {"start": {"line": 94, "column": 6}, "end": {"line": 94, "column": 19}}, "44": {"start": {"line": 100, "column": 4}, "end": {"line": 109, "column": 5}}, "45": {"start": {"line": 101, "column": 19}, "end": {"line": 101, "column": 46}}, "46": {"start": {"line": 102, "column": 6}, "end": {"line": 104, "column": 7}}, "47": {"start": {"line": 103, "column": 8}, "end": {"line": 103, "column": 36}}, "48": {"start": {"line": 105, "column": 6}, "end": {"line": 105, "column": 30}}, "49": {"start": {"line": 107, "column": 6}, "end": {"line": 107, "column": 65}}, "50": {"start": {"line": 108, "column": 6}, "end": {"line": 108, "column": 34}}, "51": {"start": {"line": 114, "column": 4}, "end": {"line": 120, "column": 5}}, "52": {"start": {"line": 115, "column": 6}, "end": {"line": 115, "column": 37}}, "53": {"start": {"line": 116, "column": 6}, "end": {"line": 116, "column": 18}}, "54": {"start": {"line": 118, "column": 6}, "end": {"line": 118, "column": 66}}, "55": {"start": {"line": 119, "column": 6}, "end": {"line": 119, "column": 19}}, "56": {"start": {"line": 125, "column": 4}, "end": {"line": 131, "column": 5}}, "57": {"start": {"line": 126, "column": 6}, "end": {"line": 126, "column": 29}}, "58": {"start": {"line": 127, "column": 6}, "end": {"line": 127, "column": 18}}, "59": {"start": {"line": 129, "column": 6}, "end": {"line": 129, "column": 61}}, "60": {"start": {"line": 130, "column": 6}, "end": {"line": 130, "column": 19}}, "61": {"start": {"line": 85, "column": 0}, "end": {"line": 85, "column": 13}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 4, "column": 2}, "end": {"line": 4, "column": 8}}, "loc": {"start": {"line": 4, "column": 40}, "end": {"line": 13, "column": 3}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 16, "column": 2}, "end": {"line": 16, "column": 8}}, "loc": {"start": {"line": 16, "column": 49}, "end": {"line": 27, "column": 3}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 30, "column": 2}, "end": {"line": 30, "column": 8}}, "loc": {"start": {"line": 30, "column": 31}, "end": {"line": 38, "column": 3}}}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 41, "column": 2}, "end": {"line": 41, "column": 8}}, "loc": {"start": {"line": 41, "column": 14}, "end": {"line": 49, "column": 3}}}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 52, "column": 2}, "end": {"line": 52, "column": 8}}, "loc": {"start": {"line": 52, "column": 20}, "end": {"line": 61, "column": 3}}}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 64, "column": 2}, "end": {"line": 64, "column": 8}}, "loc": {"start": {"line": 64, "column": 23}, "end": {"line": 81, "column": 3}}}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 87, "column": 2}, "end": {"line": 87, "column": 8}}, "loc": {"start": {"line": 87, "column": 40}, "end": {"line": 96, "column": 3}}}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 99, "column": 2}, "end": {"line": 99, "column": 8}}, "loc": {"start": {"line": 99, "column": 49}, "end": {"line": 110, "column": 3}}}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 113, "column": 2}, "end": {"line": 113, "column": 8}}, "loc": {"start": {"line": 113, "column": 31}, "end": {"line": 121, "column": 3}}}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 124, "column": 2}, "end": {"line": 124, "column": 8}}, "loc": {"start": {"line": 124, "column": 14}, "end": {"line": 132, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 19, "column": 6}, "end": {"line": 21, "column": 7}}, "type": "if", "locations": [{"start": {"line": 19, "column": 6}, "end": {"line": 21, "column": 7}}, {"start": {}, "end": {}}]}, "1": {"loc": {"start": {"line": 20, "column": 15}, "end": {"line": 20, "column": 35}}, "type": "binary-expr", "locations": [{"start": {"line": 20, "column": 15}, "end": {"line": 20, "column": 27}}, {"start": {"line": 20, "column": 31}, "end": {"line": 20, "column": 35}}]}, "2": {"loc": {"start": {"line": 25, "column": 13}, "end": {"line": 25, "column": 33}}, "type": "binary-expr", "locations": [{"start": {"line": 25, "column": 13}, "end": {"line": 25, "column": 25}}, {"start": {"line": 25, "column": 29}, "end": {"line": 25, "column": 33}}]}, "3": {"loc": {"start": {"line": 69, "column": 8}, "end": {"line": 71, "column": 9}}, "type": "if", "locations": [{"start": {"line": 69, "column": 8}, "end": {"line": 71, "column": 9}}, {"start": {}, "end": {}}]}, "4": {"loc": {"start": {"line": 102, "column": 6}, "end": {"line": 104, "column": 7}}, "type": "if", "locations": [{"start": {"line": 102, "column": 6}, "end": {"line": 104, "column": 7}}, {"start": {}, "end": {}}]}, "5": {"loc": {"start": {"line": 103, "column": 15}, "end": {"line": 103, "column": 35}}, "type": "binary-expr", "locations": [{"start": {"line": 103, "column": 15}, "end": {"line": 103, "column": 27}}, {"start": {"line": 103, "column": 31}, "end": {"line": 103, "column": 35}}]}, "6": {"loc": {"start": {"line": 108, "column": 13}, "end": {"line": 108, "column": 33}}, "type": "binary-expr", "locations": [{"start": {"line": 108, "column": 13}, "end": {"line": 108, "column": 25}}, {"start": {"line": 108, "column": 29}, "end": {"line": 108, "column": 33}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0]}}, "/Users/<USER>/Documents/GitHub/COMCROW_CSA/src/utils/validation-utils.ts": {"path": "/Users/<USER>/Documents/GitHub/COMCROW_CSA/src/utils/validation-utils.ts", "statementMap": {"0": {"start": {"line": 2, "column": 29}, "end": {"line": 5, "column": 1}}, "1": {"start": {"line": 3, "column": 21}, "end": {"line": 3, "column": 49}}, "2": {"start": {"line": 4, "column": 2}, "end": {"line": 4, "column": 32}}, "3": {"start": {"line": 2, "column": 13}, "end": {"line": 2, "column": 29}}, "4": {"start": {"line": 8, "column": 32}, "end": {"line": 34, "column": 1}}, "5": {"start": {"line": 12, "column": 27}, "end": {"line": 12, "column": 29}}, "6": {"start": {"line": 14, "column": 2}, "end": {"line": 16, "column": 3}}, "7": {"start": {"line": 15, "column": 4}, "end": {"line": 15, "column": 63}}, "8": {"start": {"line": 18, "column": 2}, "end": {"line": 20, "column": 3}}, "9": {"start": {"line": 19, "column": 4}, "end": {"line": 19, "column": 71}}, "10": {"start": {"line": 22, "column": 2}, "end": {"line": 24, "column": 3}}, "11": {"start": {"line": 23, "column": 4}, "end": {"line": 23, "column": 71}}, "12": {"start": {"line": 26, "column": 2}, "end": {"line": 28, "column": 3}}, "13": {"start": {"line": 27, "column": 4}, "end": {"line": 27, "column": 61}}, "14": {"start": {"line": 30, "column": 2}, "end": {"line": 33, "column": 4}}, "15": {"start": {"line": 8, "column": 13}, "end": {"line": 8, "column": 32}}, "16": {"start": {"line": 37, "column": 28}, "end": {"line": 53, "column": 1}}, "17": {"start": {"line": 41, "column": 41}, "end": {"line": 41, "column": 43}}, "18": {"start": {"line": 43, "column": 2}, "end": {"line": 47, "column": 5}}, "19": {"start": {"line": 44, "column": 4}, "end": {"line": 46, "column": 5}}, "20": {"start": {"line": 45, "column": 6}, "end": {"line": 45, "column": 80}}, "21": {"start": {"line": 49, "column": 2}, "end": {"line": 52, "column": 4}}, "22": {"start": {"line": 37, "column": 13}, "end": {"line": 37, "column": 28}}, "23": {"start": {"line": 56, "column": 38}, "end": {"line": 70, "column": 1}}, "24": {"start": {"line": 60, "column": 27}, "end": {"line": 60, "column": 29}}, "25": {"start": {"line": 62, "column": 2}, "end": {"line": 64, "column": 3}}, "26": {"start": {"line": 63, "column": 4}, "end": {"line": 63, "column": 61}}, "27": {"start": {"line": 66, "column": 2}, "end": {"line": 69, "column": 4}}, "28": {"start": {"line": 56, "column": 13}, "end": {"line": 56, "column": 38}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 2, "column": 29}, "end": {"line": 2, "column": 30}}, "loc": {"start": {"line": 2, "column": 56}, "end": {"line": 5, "column": 1}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 32}, "end": {"line": 8, "column": 33}}, "loc": {"start": {"line": 11, "column": 4}, "end": {"line": 34, "column": 1}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 37, "column": 28}, "end": {"line": 37, "column": 29}}, "loc": {"start": {"line": 40, "column": 4}, "end": {"line": 53, "column": 1}}}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 43, "column": 33}, "end": {"line": 43, "column": 34}}, "loc": {"start": {"line": 43, "column": 50}, "end": {"line": 47, "column": 3}}}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 56, "column": 38}, "end": {"line": 56, "column": 39}}, "loc": {"start": {"line": 59, "column": 4}, "end": {"line": 70, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 14, "column": 2}, "end": {"line": 16, "column": 3}}, "type": "if", "locations": [{"start": {"line": 14, "column": 2}, "end": {"line": 16, "column": 3}}, {"start": {}, "end": {}}]}, "1": {"loc": {"start": {"line": 18, "column": 2}, "end": {"line": 20, "column": 3}}, "type": "if", "locations": [{"start": {"line": 18, "column": 2}, "end": {"line": 20, "column": 3}}, {"start": {}, "end": {}}]}, "2": {"loc": {"start": {"line": 22, "column": 2}, "end": {"line": 24, "column": 3}}, "type": "if", "locations": [{"start": {"line": 22, "column": 2}, "end": {"line": 24, "column": 3}}, {"start": {}, "end": {}}]}, "3": {"loc": {"start": {"line": 26, "column": 2}, "end": {"line": 28, "column": 3}}, "type": "if", "locations": [{"start": {"line": 26, "column": 2}, "end": {"line": 28, "column": 3}}, {"start": {}, "end": {}}]}, "4": {"loc": {"start": {"line": 44, "column": 4}, "end": {"line": 46, "column": 5}}, "type": "if", "locations": [{"start": {"line": 44, "column": 4}, "end": {"line": 46, "column": 5}}, {"start": {}, "end": {}}]}, "5": {"loc": {"start": {"line": 44, "column": 8}, "end": {"line": 44, "column": 68}}, "type": "binary-expr", "locations": [{"start": {"line": 44, "column": 8}, "end": {"line": 44, "column": 14}}, {"start": {"line": 44, "column": 19}, "end": {"line": 44, "column": 44}}, {"start": {"line": 44, "column": 48}, "end": {"line": 44, "column": 67}}]}, "6": {"loc": {"start": {"line": 62, "column": 2}, "end": {"line": 64, "column": 3}}, "type": "if", "locations": [{"start": {"line": 62, "column": 2}, "end": {"line": 64, "column": 3}}, {"start": {}, "end": {}}]}, "7": {"loc": {"start": {"line": 62, "column": 6}, "end": {"line": 62, "column": 113}}, "type": "binary-expr", "locations": [{"start": {"line": 62, "column": 6}, "end": {"line": 62, "column": 49}}, {"start": {"line": 62, "column": 53}, "end": {"line": 62, "column": 80}}, {"start": {"line": 62, "column": 84}, "end": {"line": 62, "column": 113}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0, 0], "6": [0, 0], "7": [0, 0, 0]}}}