import React, { useState } from 'react';
import { Search, RotateCcw, Percent } from 'lucide-react';
import { SearchCriteria } from '../types';

interface SearchCriteriaScreenProps {
  onSearch: (criteria: SearchCriteria) => void;
  isLoading: boolean;
}

const SearchCriteriaScreen: React.FC<SearchCriteriaScreenProps> = ({ 
  onSearch, 
  isLoading 
}) => {
  const [criteria, setCriteria] = useState<SearchCriteria>({
    b2b: false,
    english: false,
    global: false,
    noExperience: false,
    commissionRate: 20
  });

  const handleCheckboxChange = (field: keyof SearchCriteria) => {
    setCriteria(prev => ({
      ...prev,
      [field]: !prev[field]
    }));
  };

  const handleCommissionRateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = Math.max(0, Math.min(100, parseInt(e.target.value) || 0));
    setCriteria(prev => ({
      ...prev,
      commissionRate: value
    }));
  };

  const handleReset = () => {
    setCriteria({
      b2b: false,
      english: false,
      global: false,
      noExperience: false,
      commissionRate: 20
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch(criteria);
  };

  const checkboxItems = [
    { key: 'b2b' as const, label: 'B2B Opportunities', description: 'Focus on business-to-business sales' },
    { key: 'english' as const, label: 'English Language', description: 'Opportunities requiring English communication' },
    { key: 'global' as const, label: 'Global Opportunities', description: 'Remote and international positions' },
    { key: 'noExperience' as const, label: 'No Experience Required', description: 'Entry-level positions available' }
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-2xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Find Commission Opportunities
          </h1>
          <p className="text-lg text-gray-600">
            Configure your search criteria to find the perfect sales opportunities
          </p>
        </div>

        <div className="bg-white rounded-lg shadow-md p-8">
          <form onSubmit={handleSubmit} className="space-y-8">
            {/* Filter Checkboxes */}
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                Opportunity Filters
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {checkboxItems.map(({ key, label, description }) => (
                  <div key={key} className="relative">
                    <label className="flex items-start space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors">
                      <div className="flex items-center h-5">
                        <input
                          type="checkbox"
                          checked={criteria[key]}
                          onChange={() => handleCheckboxChange(key)}
                          className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                          aria-describedby={`${key}-description`}
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="text-sm font-medium text-gray-900">
                          {label}
                        </div>
                        <div id={`${key}-description`} className="text-sm text-gray-500">
                          {description}
                        </div>
                      </div>
                    </label>
                  </div>
                ))}
              </div>
            </div>

            {/* Commission Rate */}
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                Commission Rate
              </h2>
              <div className="bg-gray-50 p-6 rounded-lg">
                <label htmlFor="commission-rate" className="block text-sm font-medium text-gray-700 mb-2">
                  Minimum Commission Rate (%)
                </label>
                <div className="relative max-w-xs">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Percent className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="number"
                    id="commission-rate"
                    min="0"
                    max="100"
                    value={criteria.commissionRate}
                    onChange={handleCommissionRateChange}
                    className="pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm"
                    placeholder="20"
                    aria-label="Minimum commission rate percentage"
                  />
                </div>
                <p className="mt-2 text-sm text-gray-500">
                  Enter a percentage between 0 and 100. Higher rates may yield fewer results.
                </p>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <button
                type="submit"
                disabled={isLoading}
                className="flex-1 flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                    Searching...
                  </>
                ) : (
                  <>
                    <Search className="h-5 w-5 mr-2" />
                    Search Opportunities
                  </>
                )}
              </button>
              
              <button
                type="button"
                onClick={handleReset}
                disabled={isLoading}
                className="flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <RotateCcw className="h-5 w-5 mr-2" />
                Reset Filters
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default SearchCriteriaScreen;