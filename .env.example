# COMCROW CSA Environment Configuration Template
# Copy this file to .env and fill in your actual values

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
VITE_APP_NAME=COMCROW CSA
VITE_APP_VERSION=1.0.0
VITE_APP_ENVIRONMENT=development

# =============================================================================
# API CONFIGURATION
# =============================================================================
# Base URL for the COMCROW API
VITE_API_BASE_URL=https://api.comcrow.com

# API timeout in milliseconds
VITE_API_TIMEOUT=30000

# API retry attempts
VITE_API_RETRY_ATTEMPTS=3

# =============================================================================
# AUTHENTICATION CONFIGURATION
# =============================================================================
# JWT token expiration time (in milliseconds)
VITE_AUTH_TOKEN_EXPIRY=86400000

# Refresh token expiration time (in milliseconds)
VITE_AUTH_REFRESH_TOKEN_EXPIRY=604800000

# OAuth providers (if applicable)
VITE_GOOGLE_CLIENT_ID=your_google_client_id_here
VITE_MICROSOFT_CLIENT_ID=your_microsoft_client_id_here

# =============================================================================
# DATABASE CONFIGURATION (Backend)
# =============================================================================
# Database connection string
DATABASE_URL=postgresql://username:password@localhost:5432/comcrow_csa

# Database pool configuration
DB_POOL_MIN=2
DB_POOL_MAX=10

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================
# Email service configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# File storage service (AWS S3, Azure Blob, etc.)
STORAGE_PROVIDER=aws
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=comcrow-csa-files

# =============================================================================
# SEARCH AND DATA PROCESSING
# =============================================================================
# Search service configuration
SEARCH_SERVICE_URL=https://search.comcrow.com
SEARCH_API_KEY=your_search_api_key

# Data extraction service
DATA_EXTRACTION_URL=https://extract.comcrow.com
DATA_EXTRACTION_API_KEY=your_extraction_api_key

# Maximum search results per query
VITE_MAX_SEARCH_RESULTS=1000

# Search timeout in milliseconds
VITE_SEARCH_TIMEOUT=60000

# =============================================================================
# ANALYTICS AND MONITORING
# =============================================================================
# Google Analytics
VITE_GA_TRACKING_ID=GA-XXXXXXXXX

# Application monitoring (Sentry, LogRocket, etc.)
VITE_SENTRY_DSN=https://<EMAIL>/project_id
VITE_LOGROCKET_APP_ID=your_logrocket_app_id

# Performance monitoring
VITE_ENABLE_PERFORMANCE_MONITORING=true

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# CORS allowed origins (comma-separated)
CORS_ORIGINS=http://localhost:3000,https://app.comcrow.com

# Rate limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Session configuration
SESSION_SECRET=your_super_secret_session_key
SESSION_TIMEOUT=3600000

# =============================================================================
# FEATURE FLAGS
# =============================================================================
# Enable/disable features
VITE_ENABLE_ADVANCED_SEARCH=true
VITE_ENABLE_EXPORT_FEATURES=true
VITE_ENABLE_REAL_TIME_UPDATES=false
VITE_ENABLE_DARK_MODE=true

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================
# Development server configuration
VITE_DEV_SERVER_PORT=3000
VITE_DEV_SERVER_HOST=localhost

# Enable development tools
VITE_ENABLE_DEV_TOOLS=true
VITE_ENABLE_REDUX_DEVTOOLS=true

# Mock data configuration
VITE_USE_MOCK_DATA=true
VITE_MOCK_DELAY=1000

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
# Log level (error, warn, info, debug)
LOG_LEVEL=info

# Log file path
LOG_FILE_PATH=./logs/app.log

# Enable console logging
ENABLE_CONSOLE_LOGGING=true

# =============================================================================
# CACHE CONFIGURATION
# =============================================================================
# Redis configuration (if using Redis for caching)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your_redis_password

# Cache TTL in seconds
CACHE_TTL=3600

# =============================================================================
# NOTIFICATION CONFIGURATION
# =============================================================================
# Push notification service
PUSH_NOTIFICATION_KEY=your_push_notification_key
PUSH_NOTIFICATION_SECRET=your_push_notification_secret

# Email notification templates
EMAIL_TEMPLATE_PATH=./templates/emails

# =============================================================================
# BACKUP AND MAINTENANCE
# =============================================================================
# Backup configuration
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_PATH=./backups

# Maintenance mode
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE=System is under maintenance. Please try again later.
