# COMCROW CSA - Directory Structure

This document outlines the organization and purpose of each directory and file in the COMCROW CSA project.

## Root Directory Structure

```
COMCROW_CSA/
├── .env                          # Environment variables for development
├── .env.example                  # Template for environment variables
├── .gitignore                    # Git ignore rules
├── index.html                    # Main HTML entry point
├── package.json                  # Node.js dependencies and scripts
├── package-lock.json             # Locked dependency versions
├── README.md                     # Project overview and setup instructions
├── assets/                       # Static assets (images, fonts, etc.)
├── config/                       # Configuration files
├── docs/                         # Project documentation
├── public/                       # Public static files
├── scripts/                      # Build and deployment scripts
├── src/                          # Source code
└── tests/                        # Test files
```

## Source Code Structure (`src/`)

```
src/
├── app.tsx                       # Main application component
├── main.tsx                      # Application entry point
├── vite-env.d.ts                # Vite environment type definitions
├── components/                   # React components organized by type
│   ├── forms/                    # Form-related components
│   │   ├── login-screen.tsx      # User login form
│   │   └── search-criteria-screen.tsx # Search criteria form
│   ├── layout/                   # Layout and navigation components
│   │   └── navigation.tsx        # Main navigation component
│   └── ui/                       # UI display components
│       ├── error-screen.tsx      # Error display component
│       └── results-screen.tsx    # Search results display
├── constants/                    # Application constants
│   └── app-constants.ts          # Global application constants
├── hooks/                        # Custom React hooks
│   ├── use-auth.ts              # Authentication hook
│   └── use-search.ts            # Search functionality hook
├── services/                     # Business logic and API services
│   ├── api-service.ts           # Base API service class
│   ├── auth-service.ts          # Authentication service
│   └── search-service.ts        # Search functionality service
├── styles/                       # Styling files
│   └── index.css                # Global styles and Tailwind imports
├── types/                        # TypeScript type definitions
│   └── index.ts                 # Application type definitions
└── utils/                        # Utility functions
    ├── format-utils.ts          # Data formatting utilities
    ├── storage-utils.ts         # Local/session storage utilities
    └── validation-utils.ts      # Form and data validation utilities
```

## Configuration Directory (`config/`)

```
config/
├── eslint.config.js             # ESLint configuration
├── postcss.config.js            # PostCSS configuration
├── tailwind.config.js           # Tailwind CSS configuration
├── tsconfig.json                # Main TypeScript configuration
├── tsconfig.app.json            # App-specific TypeScript config
├── tsconfig.node.json           # Node.js TypeScript config
└── vite.config.ts               # Vite build tool configuration
```

## Documentation Directory (`docs/`)

```
docs/
├── DIRECTORY_STRUCTURE.md       # This file - directory organization guide
├── PROJECT_RULES.md             # Development rules and guidelines
├── API_DOCUMENTATION.md         # API endpoints and usage (future)
├── DEPLOYMENT_GUIDE.md          # Deployment instructions (future)
└── USER_GUIDE.md               # End-user documentation (future)
```

## Public Directory (`public/`)

```
public/
├── icons/                       # Application icons
└── images/                      # Static images
```

## Scripts Directory (`scripts/`)

```
scripts/
├── build.sh                    # Production build script (future)
├── deploy.sh                   # Deployment script (future)
├── setup.sh                    # Development setup script (future)
└── test.sh                     # Test execution script (future)
```

## Tests Directory (`tests/`)

```
tests/
├── components/                  # Component tests (future)
├── hooks/                      # Hook tests (future)
├── services/                   # Service tests (future)
├── utils/                      # Utility tests (future)
└── e2e/                        # End-to-end tests (future)
```

## File Naming Conventions

### General Rules
- Use lowercase with hyphens for separation (kebab-case)
- Make names descriptive and self-explanatory
- Follow language-specific conventions where applicable

### Specific Conventions
- **React Components**: `component-name.tsx`
- **Hooks**: `use-hook-name.ts`
- **Services**: `service-name-service.ts`
- **Utilities**: `utility-name-utils.ts`
- **Types**: `type-name.ts` or `index.ts` for grouped types
- **Constants**: `constant-name-constants.ts`
- **Styles**: `style-name.css` or `index.css` for main styles

## Directory Organization Logic

### Components (`src/components/`)
Components are organized by their primary function:
- **forms/**: Components that handle user input and form submission
- **layout/**: Components that provide page structure and navigation
- **ui/**: Components that display data and provide user interface elements

### Services (`src/services/`)
Services contain business logic and external integrations:
- **api-service.ts**: Base service for HTTP requests
- **auth-service.ts**: Authentication and user management
- **search-service.ts**: Search functionality and data processing

### Utils (`src/utils/`)
Utilities are pure functions that can be used across the application:
- **format-utils.ts**: Data formatting and display helpers
- **storage-utils.ts**: Browser storage management
- **validation-utils.ts**: Input validation and form helpers

## Adding New Files

### When adding new components:
1. Determine the component's primary purpose
2. Place in the appropriate subdirectory under `src/components/`
3. Use kebab-case naming convention
4. Export from the component file and update imports as needed

### When adding new services:
1. Create in `src/services/` directory
2. Follow the naming pattern: `feature-service.ts`
3. Implement as a class with singleton pattern if stateful
4. Export both the class and a singleton instance

### When adding new utilities:
1. Group related utilities in files under `src/utils/`
2. Use descriptive names ending in `-utils.ts`
3. Export individual functions (not classes)
4. Keep functions pure and stateless

### When adding new types:
1. Add to `src/types/index.ts` for shared types
2. Create separate files for complex type definitions
3. Use PascalCase for interface and type names
4. Group related types together

## Configuration Files

All configuration files are centralized in the `config/` directory with symbolic links in the root for tools that require root-level configuration files. This approach:
- Keeps the root directory clean
- Centralizes all configuration
- Maintains tool compatibility through symbolic links
- Makes configuration management easier

## Best Practices

1. **Separation of Concerns**: Each directory has a specific purpose
2. **Consistent Naming**: Follow established naming conventions
3. **Logical Grouping**: Related files are grouped together
4. **Scalability**: Structure supports growth and new features
5. **Maintainability**: Clear organization makes code easier to maintain

## Future Expansion

As the project grows, consider adding these directories:
- `src/contexts/` - React context providers
- `src/store/` - State management (Redux, Zustand, etc.)
- `src/middleware/` - Custom middleware functions
- `src/workers/` - Web workers for background processing
- `src/assets/` - Source assets that need processing
